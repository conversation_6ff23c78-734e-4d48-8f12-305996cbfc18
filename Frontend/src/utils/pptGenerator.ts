import PptxGenJS from "pptxgenjs";

const generatePPT = (plots:any, preds:any, inputs:any, pieceId:any) => {
  const pptx = new PptxGenJS();
  const today = new Date().toLocaleDateString();

  // Title slide
  const slide = pptx.addSlide();
  slide.addText("RollingAI Report", { x: 0.5, y: 0.5, fontSize: 24 });
  slide.addText(`Piece ID: ${pieceId}\nDate: ${today}`, { x: 0.5, y: 1.2, fontSize: 14 });

  // Input variables slide
  const inputSlide = pptx.addSlide();
  inputSlide.addText("Input Variables", { x: 0.5, y: 0.5, fontSize: 20 });

  // Filter out null/undefined values
  const validInputs = Object.entries(inputs).filter(([_, val]) => val !== null && val !== undefined);

  // Calculate how many slides we need
  const maxPerSlide = 45; // 15 rows x 3 columns
  const totalSlides = Math.ceil(validInputs.length / maxPerSlide);

  for (let slideIndex = 0; slideIndex < totalSlides; slideIndex++) {
    // Create a new slide for each batch except the first one which is already created
    const currentSlide = slideIndex === 0 ? inputSlide : pptx.addSlide();

    if (slideIndex > 0) {
      currentSlide.addText("Input Variables (continued)", { x: 0.5, y: 0.5, fontSize: 20 });
    }

    // Get the inputs for this slide
    const startIndex = slideIndex * maxPerSlide;
    const endIndex = Math.min(startIndex + maxPerSlide, validInputs.length);
    const slideInputs = validInputs.slice(startIndex, endIndex);

    // Calculate how many columns we need for this slide
    const itemsPerColumn = 15;
    const columnsNeeded = Math.min(3, Math.ceil(slideInputs.length / itemsPerColumn));

    // Column widths and positions
    const columnPositions = [0.5, 4.0, 7.5]; // x positions for each column

    // Distribute inputs across columns
    for (let colIndex = 0; colIndex < columnsNeeded; colIndex++) {
      const colStartIndex = colIndex * itemsPerColumn;
      const colEndIndex = Math.min(colStartIndex + itemsPerColumn, slideInputs.length);
      const columnInputs = slideInputs.slice(colStartIndex, colEndIndex);

      // Add each input to the appropriate column
      columnInputs.forEach(([key, val], rowIndex) => {
        currentSlide.addText(`${key}: ${val}`, {
          x: columnPositions[colIndex],
          y: 1 + rowIndex * 0.3,
          fontSize: 10,
          w: 3.0 // Set width to prevent text overflow
        });
      });
    }
  }

  // Create slides for each metric
  const metrics = Object.keys(preds);

  metrics.forEach(metric => {
    const resultSlide = pptx.addSlide();
    resultSlide.addText(`${metric} Prediction: ${preds[metric]} ${metric === 'Elongation' ? '%' : 'MPa'}`,
      { x: 0.5, y: 0.5, fontSize: 18 });

    // Add plot titles
    resultSlide.addText(`SHAP Impact Analysis`, {
      x: 1,
      y: 1,
      fontSize: 14,
      bold: true,
      color: "252963"
    });

    resultSlide.addText(`Decision Plot Analysis`, {
      x: 6,
      y: 1,
      fontSize: 14,
      bold: true,
      color: "252963"
    });

    // Add SHAP plot if available
    if (plots[`${metric}_shap`]) {
      try {
        // Get the base64 data
        const base64Data = plots[`${metric}_shap`];

        // Add the base64 header that pptxgenjs expects
        const imageData = `data:image/png;base64,${base64Data}`;

        // Add SHAP plot on the left side
        resultSlide.addImage({
          data: imageData,
          x: 0.5,
          y: 1.5,
          w: 3.5,
          h: 4
        });
      } catch (error) {
        console.error(`Error adding SHAP image for ${metric}:`, error);
        resultSlide.addText(`Error adding SHAP plot for ${metric}`, {
          x: 2.5,
          y: 3,
          fontSize: 12,
          color: "FF0000"
        });
      }
    }

    // Add Decision plot if available
    if (plots[`${metric}_decision`]) {
      try {
        // Get the base64 data
        const base64Data = plots[`${metric}_decision`];

        // Add the base64 header that pptxgenjs expects
        const imageData = `data:image/png;base64,${base64Data}`;

        // Add Decision plot on the right side
        resultSlide.addImage({
          data: imageData,
          x: 5.5,
          y: 1.5,
          w: 3.5,
          h: 4
        });
      } catch (error) {
        console.error(`Error adding Decision image for ${metric}:`, error);
        resultSlide.addText(`Error adding Decision plot for ${metric}`, {
          x: 7.5,
          y: 3,
          fontSize: 12,
          color: "FF0000"
        });
      }
    }
  });

  // Summary slide
  const summary = pptx.addSlide();
  summary.addText("Summary", { x: 0.5, y: 0.5, fontSize: 20 });

  let summaryText = "";
  metrics.forEach(metric => {
    summaryText += `${metric}: ${preds[metric]} ${metric === 'Elongation' ? '%' : 'MPa'}\n`;
  });

  summary.addText(summaryText, { x: 0.5, y: 1.2, fontSize: 16 });

  // Save the presentation
  pptx.writeFile({fileName: `RollingAI_Report_${pieceId}.pptx`});
};

export default generatePPT;
