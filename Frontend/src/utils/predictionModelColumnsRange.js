const ColumnsRanges = [
    // { column_name: "Al_N_Ratio", min: 0.3623, max: 4.5303 , step:0.1},
    { column_name: "B", min: 0, max: 4 , step:0.1, hidden: true},
    { column_name: "BD_Area Reduction_3", min: 1.2, max: 13.8 , step:0.1},
    { column_name: "BD_Area Reduction_4", min: 0.1, max: 14.5 , step:0.1},
    { column_name: "BD_Area Reduction_5", min: 0.8, max: 18.09 , step:0.1},
    { column_name: "BD_Entry Speed_1", min: 1.8, max: 2.2 , step:0.1},
    { column_name: "BD_Entry Speed_4", min: 1.6, max: 2.2 , step:0.1},
    { column_name: "BD_Entry Speed_5", min: 1.7, max: 2.2 , step:0.1},
    { column_name: "BD_Roll Gap_3", min: 33.0, max: 263.0 , step:1},
    { column_name: "BD_Roll Gap_4", min: 16.0, max: 360.0 , step:1},
    { column_name: "DeltaT_FRT_NonReX", min: -153.96, max: 103.13 , step:1},
    { column_name: "Mn_C_Ratio", min: 3.1, max: 13.08 , step:0.1},
    { column_name: "Mn_S_Ratio", min: 50.0, max: 1590.0 , step:1},
    { column_name: "Nb", min: 0.0, max: 0.013 , step:0.001},
    { column_name: "Semis Length", min: 5388.0, max: 12162.0 , step:10, hidden: true},
    { column_name: "Semis Weight", min: 8500.0, max: 12200.0 , step:10, hidden: true},
    { column_name: "Si", min: 0.15, max: 0.3 , step:0.01},
    { column_name: "Thickness", min: 8.4, max: 52.37 , step:1},
    { column_name: "V", min: 0.0011, max: 0.088 , step:0.001},
    // { column_name: "weight", min: 41.2, max: 393.0 , step:1, hidden: true},
    { column_name: "Charging Temp", min: 0.0, max: 950.0 , step:1, hidden: true},
    { column_name: "TM Exit Avg Temp", min: 688.0, max: 970.0 , step:1},
    { column_name: "C", min: 0.12, max: 0.24 , step:0.01},
    { column_name: "Ti", min: 0.0005, max: 0.0042 , step:0.0001, hidden: true},
    { column_name: "Al", min: 0.0022, max: 0.0299 , step:0.0001},
    { column_name: "Mn", min: 0.6, max: 1.6 , step:0.1},
    { column_name: "S", min: 0.0, max: 0.019 , step:0.001, hidden: true},
    { column_name: "N", min: 49, max: 88 , step:1},
    { column_name: "Cu", min: 0, max: 0.05 , step:0.01, hidden: true},
    { column_name: "RHF Res Time", min: 100, max: 520 , step:1}
  ];
  

export {
  ColumnsRanges
}
