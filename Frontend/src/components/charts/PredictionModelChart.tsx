import React, { useEffect, useState } from 'react';
import Plot from 'react-plotly.js';
import { getRequest, putRequest } from '../../utils/apiHandler';
import { InputNumber, Select, Skeleton, Slider, SliderSingleProps, message, Button } from 'antd';
import { ColumnsRanges } from '../../utils/predictionModelColumnsRange';
import { Navigate, useLocation, useNavigate } from "react-router-dom";
import { DownloadOutlined } from '@ant-design/icons';
import generatePPT from '../../utils/pptGenerator';
interface PredictionModelChartProps {
    data: any;
    inputParameters: any;
    componentSetting: any;
}

export const PredictionModelChart: React.FC<PredictionModelChartProps> = ({ data, inputParameters, componentSetting }) => {
    console.log('componentSetting :', componentSetting);
    const [activeTab, setActiveTab] = useState<string>('Yield');
    const navigate = useNavigate();
    const location = useLocation()
    const [pieceIds, setPieceIds] = useState<any>([])
    const [selectedPieceId, setSelectedPieceId] = useState<any>([])
    const [loading, setLoading] = useState<boolean>(false)
    const [section, setSection] = useState<string | null>(null)
    const [semis, setSemis] = useState<string | null>(null)
    const [beamTypes, setBeamTypes] = useState<string | null>(null)
    const [weight, setWeight] = useState<string | null>(null)
    const [chargingType, setChargingType] = useState<string | null>(null)
    const [castGrade, setCastGrade] = useState<string | null>(null)
    const [pieceIdParameters, setPieceIdParameters] = useState<any>([])
    const [csvId, setCsvId] = useState(null)
    useEffect(() => {
        const parts = inputParameters.file_path?.split('/');
        if (parts) {
            const csvId = parts[parts.length - 1];
            setCsvId(csvId)
        }
        // if(operationType=='prediction_model' && csvId){
        // }else{
        //   setPieceIds([])
        //   setSection(null)
        //   setSemis(null)
        //   setBeamTypes(null)
        // }
        setResponse()
        getPieceIds()
    }, []);

    const getPieceIds = async () => {
        try {
            const parts = inputParameters.file_path.split('/');
            const csvId = parts[parts.length - 1];
            if (!csvId) return
            const response = await getRequest(`/file/piece-ids/${csvId}`)
            if (response.status == 200) {
                setPieceIds(response?.data?.data?.pieceIds)
            } else {
                setPieceIds([])
                // setPieceIdParameters([])
            }

        } catch (error) {
            console.log('error :', error);

        }
    }

    const setResponse = () => {
        setPieceIdParameters(inputParameters.input_parameters
            // Filter out parameters that are not in ColumnsRanges
            .filter((param: any) => ColumnsRanges.map((item: any) => item.column_name.toLowerCase()).includes(param.column_name.toLowerCase()))
            // Filter out hidden parameters
            // .filter((param: any) => !ColumnsRanges.filter((item: any) => item.hidden && item.hidden === true).map((item) => item.column_name.toLowerCase()).includes(param.column_name.toLowerCase()))
        );
        setSelectedPieceId(componentSetting?.predictionModel?.pieceId)
        setSection(componentSetting?.predictionModel?.section)
        setSemis(componentSetting?.predictionModel?.semis)
        setBeamTypes(componentSetting?.predictionModel?.beamTypes)
        setWeight(componentSetting?.predictionModel?.weight)
        setChargingType(componentSetting?.predictionModel?.chargingType)
        setCastGrade(componentSetting?.predictionModel?.castGrade)
    }



    const generateMarks = (min: number, max: number, current: number): SliderSingleProps['marks'] => {
        return {
            [min]: <span className="slider-min-max">{min}</span>,
            // [current]: <strong className="slider-current-value">{current}</strong>,
            [max]: <span className="slider-min-max">{max}</span>,
        };
    };

    const handleSliderChange = (index: number, newValue: number) => {
        // Update the value of the pieceIdParameters array at the specific index
        if (!newValue) return
        setPieceIdParameters((prevParameters: any) => {
            const updatedParameters = [...prevParameters];
            updatedParameters[index] = {
                ...updatedParameters[index],
                value: newValue // Update the specific parameter's value
            };
            return updatedParameters;
        });
    };

    const handleSave = async () => {
        const queryParams = new URLSearchParams(location.search);
        const workflowId = queryParams.get('workflowId');
        let payload = {
            predictionModel: {
                pieceIdParameters: pieceIdParameters,
                section: section,
                semis: semis,
                pieceId: selectedPieceId,
                beamTypes: beamTypes,
                weight: weight,
                castGrade: castGrade,
                chargingType: chargingType
            }
        }
        const response = await putRequest(`/workflow/input-params/${workflowId}`, payload)
        const { status, message: apiMessage, data } = response.data;
        if (status === 200) {
            message.success('Input params saved successfully');

        } else {
            message.error(apiMessage);
        }
    }

    const handleExecute = async () => {
        await handleSave()
        const queryParams = new URLSearchParams(location.search);
        const workflowId = queryParams.get('workflowId');
        const response = await getRequest(`/workflow/${workflowId}/execute`)
        navigate(`?tab=insight&workflowId=${workflowId}&runsListing=true`, { replace: true });
    }

    const filterAndConvertToNumbers = (data: any[]): any[] => {
        const validColumns = new Set(ColumnsRanges.map(col => col.column_name.toLowerCase()));

        return data
            .filter(item => {
                const num = Number(item.value);
                return (
                    !isNaN(num) &&
                    isFinite(num) &&
                    item.column_name.toLowerCase() !== "piece id" &&
                    validColumns.has(item.column_name.toLowerCase())
                );
            })
            .map(item => ({
                column_name: item.column_name,
                value: Number(item.value)
            }));
    };


    const handlePieceIDChange = async (option: any) => {
        try {
            console.log(option, 'option');
            // const csvId = '4cac2e7a-8ebd-470e-a176-ce7c37795569'
            // const csvId = '1de8b04f-d62a-476c-b507-8d97d52e077e'
            setLoading(true)
            setSelectedPieceId(option)
            const response = await getRequest(`/file/pieceId-rowdata/${option}/${csvId}`)
            console.log('response :', response);
            if (response.status == 200) {
                const beamTypesList = [
                    'IPE',
                    'HEA',
                    'HEB',
                    'HEM',
                    'W',
                    'UB',
                    'UC',
                    'HP',
                    'UPN',
                    'UPSL',
                    'NPB',
                    'WPB',
                    'H',
                ];
                // setPieceIdRowData(response?.data?.data?.rowData)
                const section = response?.data?.data?.rowData.find((item: { column_name: string; }) => item.column_name.toLowerCase() == "section")?.value
                const semis = response?.data?.data?.rowData.find((item: { column_name: string; }) => item.column_name.toLowerCase() == "semis")?.value
                const beamTypes = beamTypesList.find(b => String(section).includes(b)) || 'Unknown';
                const weight = ((w => isNaN(w) ? null : w)(Number(String(section).split('_').pop())));
                const chargingTemp = response?.data?.data?.rowData.find((item: { column_name: string; }) => item.column_name.toLowerCase() == "charging temp")?.value
                const chargingType = chargingTemp < 200 ? 'Cold Charge' : 'Hot Charge';
                const castGrade = response?.data?.data?.rowData.find((item: { column_name: string; }) => item.column_name.toLowerCase() == "cast grade")?.value

                setSection(section)
                setSemis(semis)
                setBeamTypes(beamTypes)
                setWeight(String(weight))
                setChargingType(chargingType)
                setCastGrade(castGrade)
                const rowData = await filterAndConvertToNumbers(response?.data?.data?.rowData)
                setPieceIdParameters(rowData)
                setLoading(false)
            } else {
                // setPieceIdParameters([])
                setSection(null)
                setSemis(null)
                setBeamTypes(null)
                setWeight(null)
                setChargingType(null)
                setCastGrade(null)
                setLoading(false)
            }

        } catch (error) {
            console.log('error :', error);
            setLoading(false)
        }
    }

    if (!data) {
        return <div>No prediction model data available</div>;
    }

    const { waterfall_plots, decision_plots, predictions, force_plots } = data;
    console.log('waterfall_plots', waterfall_plots)
    console.log('decision_plots', decision_plots)

    // Get available metrics (Yield, Tensile, Elongation)
    const metrics = Object.keys(predictions || {});

    // Create PDP plot for the active metric with both scatter and line
    const renderPDPPlot = () => {
        if (!waterfall_plots || !waterfall_plots[activeTab]) return null;

        const pdpData = waterfall_plots[activeTab];

        // Create two traces - one for scatter points and one for the line
        const plotData = [
            // Scatter points
            {
                x: pdpData.x_data,
                y: pdpData.y_data.map((val: string) => parseFloat(val)),
                type: 'scatter',
                mode: 'markers',
                name: `${activeTab} Points`,
                marker: {
                    color: '#4682B4',
                    size: 8
                }
            },
            // Line
            {
                x: pdpData.x_data,
                y: pdpData.y_data.map((val: string) => parseFloat(val)),
                type: 'scatter',
                mode: 'lines',
                name: `${activeTab} Trend`,
                line: {
                    color: '#252963',
                    width: 2
                }
            }
        ];

        const layout = {
            title: {
                text: `Partial Dependence Plot for ${activeTab}`,
                font: { size: 20 },
                x: 0.5
            },
            xaxis: {
                title: { text: pdpData.x_label || 'Feature Value' },
                zeroline: true,
                gridcolor: '#E5E5E5'
            },
            yaxis: {
                title: { text: pdpData.y_label || 'Partial Dependence' },
                zeroline: true,
                gridcolor: '#E5E5E5'
            },
            margin: {
                l: 60,
                r: 30,
                b: 60,
                t: 50,
                pad: 4
            },
            legend: {
                orientation: 'h',
                y: -0.2
            }
        };

        const config = {
            responsive: true,
            displayModeBar: false
        };

        return (
            <Plot
                data={plotData as any}
                layout={layout as any}
                config={config}
                style={{ width: '100%', height: '400px' }}
            />
        );
    };

    // Create Angular Gauge Chart for the active metric
    const renderGaugeChart = () => {
        if (!predictions || !predictions[activeTab]) return null;

        // Define ranges for each metric
        const ranges: { [key: string]: { min: number; max: number; } } = {
            'Yield': { min: 275, max: 600 },
            'Tensile': { min: 300, max: 800 },
            'Elongation': { min: 0, max: 40 }
        };

        const range = ranges[activeTab] || { min: 0, max: 100 };
        const value = predictions[activeTab];

        // Calculate percentage for the gauge
        const percentage = ((value - range.min) / (range.max - range.min)) * 100;

        // Define colors based on the percentage
        let gaugeColor = '#4CAF50'; // Green for good values
        if (percentage < 25) {
            gaugeColor = '#F44336'; // Red for low values
        } else if (percentage < 50) {
            gaugeColor = '#FF9800'; // Orange for medium-low values
        } else if (percentage > 90) {
            gaugeColor = '#2196F3'; // Blue for high values
        }

        // Create the gauge chart
        const plotData = [
            {
                type: 'indicator',
                mode: 'gauge+number',
                value: value,
                title: { text: 'Elongation' !== activeTab ? `${activeTab} Strength (MPa)` : `${activeTab} (%)`, font: { size: 24 } },
                gauge: {
                    axis: {
                        range: [range.min, range.max],
                        tickwidth: 1,
                        tickcolor: '#888'
                    },
                    bar: { color: gaugeColor },
                    bgcolor: 'white',
                    borderwidth: 2,
                    bordercolor: '#ccc',
                    steps: [
                        { range: [range.min, range.min + (range.max - range.min) * 0.25], color: 'rgba(244, 67, 54, 0.2)' },
                        { range: [range.min + (range.max - range.min) * 0.25, range.min + (range.max - range.min) * 0.5], color: 'rgba(255, 152, 0, 0.2)' },
                        { range: [range.min + (range.max - range.min) * 0.5, range.min + (range.max - range.min) * 0.75], color: 'rgba(76, 175, 80, 0.2)' },
                        { range: [range.min + (range.max - range.min) * 0.75, range.max], color: 'rgba(33, 150, 243, 0.2)' }
                    ],
                    threshold: {
                        line: { color: 'red', width: 4 },
                        thickness: 0.75,
                        value: value
                    }
                }
            }
        ];

        const layout = {
            width: 400,
            height: 300,
            margin: { t: 40, r: 25, l: 25, b: 25 },
            paper_bgcolor: 'white',
            font: { color: '#333', family: 'Arial' }
        };

        const config = {
            responsive: true,
            displayModeBar: false
        };

        return (
            <div style={{ display: 'flex', justifyContent: 'center', width: '100%' }}>
                <Plot
                    data={plotData as any}
                    layout={layout as any}
                    config={config}
                    style={{ width: '400px', height: '300px' }}
                />
            </div>
        );
    };

    // Create SHAP plot for the active metric
    const renderSHAPPlot = () => {
        if (!waterfall_plots || !waterfall_plots[activeTab]) return null;

        const shapData = waterfall_plots[activeTab];
        const baseValue = shapData.base_value;

        const shapValues = shapData.x_data;
        // Split the y_data into feature names and duplicate them for positive/negative values
        const featureNames = shapData.y_data.slice(0, shapData.y_data.length / 2);

        // Updated x: final contribution values (base + shap)
        const finalX = shapValues.map((val: number) => baseValue + val);

        // Relative offset from base (keep bars starting at baseValue)
        const baseArray = Array(shapValues.length).fill(baseValue);

        // Create horizontal bar chart
        const plotData = [{
            type: 'bar',
            orientation: 'h',
            x: shapValues,
            y: featureNames,
            base: baseArray,
            marker: {
                color: shapData.x_data.map((val: number) => val >= 0 ? '#ff2975' : '#4A90E2')
            },
            text: shapData.x_data.map((val: number) => val.toFixed(3)),
            textposition: 'auto',
            hoverinfo: 'x+y',
            hovertemplate: '<b>%{y}</b><br>SHAP Value: %{x:.3f}<extra></extra>'
        },
        {
            type: 'scatter',
            mode: 'lines',
            x: [baseValue, baseValue],
            y: [featureNames[0], featureNames[featureNames.length - 1]],
            line: {
                color: 'black',
                width: 2,
                dash: 'dash'
            },
            showlegend: false,
            hoverinfo: 'skip'
        }];

        const layout = {
            title: {
                text: `Waterfall Plot - SHAP Impact for ${activeTab}`,
                font: { size: 20 },
                x: 0.5
            },
            xaxis: {
                title: { text: 'f(x)' },
                zeroline: false,
                gridcolor: '#E5E5E5'
            },
            yaxis: {
                automargin: true,
                autorange: 'reversed' // <--- This mirrors the plot vertically
            },
            margin: {
                l: 150,
                r: 30,
                b: 60,
                t: 50,
                pad: 4
            },
            showlegend: false,
            annotations: [
                {
                    x: baseValue,
                    y: featureNames[0],
                    xref: 'x',
                    yref: 'y',
                    text: `Base Value: ${parseFloat(baseValue).toFixed(3)}`,
                    showarrow: true,
                    arrowhead: 2,
                    ax: 40,
                    ay: -40,
                    font: { size: 12, color: 'black' },
                    bgcolor: 'white',
                    bordercolor: 'black',
                    borderwidth: 1
                }
            ]
        };

        const config = {
            responsive: true,
            displayModeBar: false,
            toImageButtonOptions: {
                format: 'png' as 'png',
                filename: `SHAP_${activeTab}`,
                height: 800,
                width: 1200,
                scale: 1
            }
        };

        return (
            <Plot
                data={plotData as any}
                layout={layout as any}
                config={config}
                style={{ width: '100%', height: '800px' }}
            />
        );
    };

    // Create Decision Plot for the active metric
    const renderDecisionPlot = () => {

        if (!decision_plots || !decision_plots[activeTab]) return null;

        const shapData = decision_plots[activeTab];
        const baseValue = shapData.base_value;

        // Split the y_data into feature names
        const featureNames = shapData.y_data.slice(0, shapData.y_data.length / 2);

        // Create the decision plot
        const plotData = [
            {
                type: 'scatter',
                mode: 'lines+markers',
                x: shapData.x_data,
                y: featureNames,
                line: {
                    color: '#d62728',
                    width: 2,
                },
                marker: {
                    color: shapData.x_data,
                    size: 8,
                    colorscale: 'Viridis', // Add a color scale
                    colorbar: {
                        title: 'Model Output Value',
                        titleside: 'top',
                        orientation: 'h', // Set the colorbar orientation to horizontal
                        thickness: 15,
                        x: 0.5, // Center the colorbar horizontally
                        xanchor: 'center',
                        y: 1.10, // Position the colorbar above the plot
                        yanchor: 'top',
                    },
                },
                hoverinfo: 'x+y',
                hovertemplate: '<b>%{y}</b><br>Model Output Value: %{x:.3f}<extra></extra>',
            },
            {
                type: 'scatter',
                mode: 'lines',
                x: [baseValue, baseValue],
                y: [featureNames[0], featureNames[featureNames.length - 1]],
                line: {
                    color: 'black',
                    width: 2,
                    dash: 'dash'
                },
                showlegend: false,
                hoverinfo: 'skip'
            }
        ];

        const layout = {
            title: {
                text: `Decision Plot - ${activeTab}`,
                font: { size: 20 },
                x: 0.5,
            },
            xaxis: {
                title: { text: 'Model Output Value' },
                zeroline: true,
                gridcolor: '#E5E5E5',
            },
            yaxis: {
                // title: { text: 'Features' },
                automargin: true,
                autorange: 'reversed', // Reverse the y-axis to match the decision plot style
            },
            margin: {
                l: 150,
                r: 50,
                b: 60,
                t: 150, // Increase the top margin to accommodate the colorbar
                pad: 4,
            },
            showlegend: false,
            annotations: [
                {
                    x: baseValue,
                    y: 0,
                    xref: 'x',
                    yref: 'y',
                    text: `Base Value: ${parseFloat(baseValue).toFixed(3)}`,
                    showarrow: true,
                    arrowhead: 2,
                    ax: 40,
                    ay: -40,
                    font: { size: 12, color: 'black' },
                    bgcolor: 'white',
                    bordercolor: 'black',
                    borderwidth: 1
                }
            ]
        };

        const config = {
            responsive: true,
            displayModeBar: false,
            toImageButtonOptions: {
                format: 'png' as 'png',
                filename: `Decision_${activeTab}`,
                height: 800,
                width: 1200,
                scale: 1
            }
        };

        return (
            <Plot
                data={plotData as any}
                layout={layout as any}
                config={config}
                style={{ width: '100%', height: '800px' }}
            />
        );
    };

    // Create Force Plot for the active metric
    const renderForcePlot = () => {
        if (!force_plots || !force_plots[activeTab]) return null;
        const forceData = force_plots[activeTab];
        const { html_b64 } = forceData;
        console.log('forceData', forceData);

        const imgSrc = `data:text/html;base64,${html_b64}`;
        // console.log('imgSrc: ', imgSrc);
        return (
            <iframe
                src={imgSrc}
                title="SHAP Force Plot"
                width="100%"
                height="200px"
                style={{ border: 'none' }}
            />
        );
    }

    // Function to handle PPT generation
    const handleDownloadPPT = async () => {
        try {
            message.loading({ content: 'Generating PowerPoint...', key: 'pptDownload' });

            // Create an object to store all plot images
            const plotImages: { [key: string]: string } = {};

            // Get all metrics
            const metrics = Object.keys(predictions || {});

            // For each metric, capture plots using Plotly's toImage function
            for (const metric of metrics) {
                // Set active tab to current metric to ensure plots are rendered
                setActiveTab(metric);

                // Wait a moment for plots to render
                await new Promise(resolve => setTimeout(resolve, 1000));

                // Find all Plotly plots
                const plotElements = document.querySelectorAll('.js-plotly-plot');

                if (plotElements.length >= 2) {
                    try {
                        // First plot is usually SHAP plot
                        // @ts-ignore - Plotly adds this method to window
                        const shapImage = await window.Plotly.toImage(plotElements[0], {
                            format: 'png',
                            width: 1200,
                            height: 800
                        });


                        // Store just the base64 part without the data URL prefix
                        if (shapImage.includes('base64,')) {
                            const base64Data = shapImage.split('base64,')[1];
                            plotImages[`${metric}_shap`] = base64Data;
                        } else {
                            plotImages[`${metric}_shap`] = shapImage;
                        }

                        // Second plot is usually Decision plot
                        // @ts-ignore - Plotly adds this method to window
                        const decisionImage = await window.Plotly.toImage(plotElements[1], {
                            format: 'png',
                            width: 1200,
                            height: 800
                        });


                        // Store just the base64 part without the data URL prefix
                        if (decisionImage.includes('base64,')) {
                            const base64Data = decisionImage.split('base64,')[1];
                            plotImages[`${metric}_decision`] = base64Data;
                        } else {
                            plotImages[`${metric}_decision`] = decisionImage;
                        }
                    } catch (err) {
                        console.error(`Error capturing plots for ${metric}:`, err);
                    }
                } else {
                    console.warn(`Could not find Plotly elements for metric ${metric}`);
                }
            }

            // Format input parameters for PPT
            const formattedInputs: { [key: string]: any } = {};
            pieceIdParameters.forEach((param: any) => {
                formattedInputs[param.column_name] = param.value;
            });

            // Add additional parameters
            formattedInputs['Section'] = section;
            formattedInputs['Semis'] = semis;
            formattedInputs['Beam Type'] = beamTypes;
            formattedInputs['Weight'] = weight;
            formattedInputs['Charging Type'] = chargingType;
            formattedInputs['Cast Grade'] = castGrade;

            // Generate the PPT
            generatePPT(plotImages, predictions, formattedInputs, selectedPieceId || 'Unknown');

            message.success({ content: 'PowerPoint generated successfully!', key: 'pptDownload' });
        } catch (error) {
            console.error('Error generating PPT:', error);
            message.error({ content: 'Failed to generate PowerPoint', key: 'pptDownload' });
        }
    };

    return (
        <div className="w-full p-4">
            {/* Top metrics section */}
            <div className="">
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
                    {metrics.map(metric => (
                        <div key={metric} className={`flex flex-col items-center bg-gray-100 rounded p-5 ${activeTab === metric ? 'bg-primary' : ''}`}>
                            <span className={`text-5xl font-bold text-center mb-2 ${activeTab === metric ? 'text-white' : 'text-primary'}`}>{predictions[metric]}</span>
                            <span className={`text-gray-700 text-lg font-medium ${activeTab === metric ? 'text-white' : 'text-primary'}`}>{metric}{'Elongation' !== metric ? ' Strength (MPa)' : ' (%)'}</span>
                        </div>
                    ))}
                </div>
            </div>

            {/* Tabs section */}
            <div className="flex border-b border-gray-300 pt-3">
                {metrics.map(metric => (
                    <button
                        key={metric}
                        className={`px-3 py-2 border border-gray-300 rounded-t-lg ${activeTab === metric ? 'bg-blue-200' : 'bg-white'
                            } text-sm font-medium`}
                        onClick={() => setActiveTab(metric)}
                    >
                        {metric}{'Elongation' !== metric ? ' Strength' : ''}
                    </button>
                ))}
                <button
                    key={Date.now()}
                    className={`px-3 py-2 border border-gray-300 rounded-t-lg ml-auto ${activeTab === 'input_params' ? 'bg-blue-200' : 'bg-white'} text-sm font-medium`}

                    onClick={() => {
                        setActiveTab('input_params')
                        // handlePieceIDChange(selectedPieceId)
                    }}
                >
                    Input Parameters
                    {/* {metric}{'Elongation' !== metric ? ' Strength' : ''} */}
                </button>
            </div>


            {activeTab == 'input_params' ? (
                <div>
                    <div className="mb-2">
                        <div className="bg-blue-10 p-8 rounded-lg shadow-md w-full mx-auto">
                            <h2 className="text-2xl font-bold text-center mb-2">Prediction Settings</h2>
                            <hr className="border-t border-gray-400 mb-6 w-full" />

                            <div className="mb-8">
                                <h3 className="text-lg font-semibold mb-4">Filters</h3>
                                <div className="mb-4 p-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Select Piece ID
                                        </label>
                                        <Select
                                            placeholder="Select Piece ID"
                                            style={{ width: '100%' }}
                                            value={selectedPieceId || undefined}
                                            onChange={(option) => handlePieceIDChange(option)}
                                            options={pieceIds.map((pieceId: any) => ({
                                                value: pieceId,
                                                label: pieceId
                                            }))}
                                        />
                                    </div>
                                </div>
                                <div className="grid grid-cols-3 sm:grid-cols-3 md:grid-cols-3 gap-4">
                                    <div className="border h-fit border-[#252963] rounded-lg ml-4 mr-4 px-4 py-1 text-sm text-black">
                                        <div className="flex justify-between items-center">
                                            <p className="text-lg">Section</p>
                                            <div className="flex items-center justify-center bg-gray-200 text-center text-black text-sm px-4 py-1 rounded overflow-hidden">
                                                {loading ? (
                                                    <Skeleton.Input active size="small" />
                                                ) : (
                                                    <span className="text-sm">{section}</span>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                    <div className="border h-fit border-[#252963] rounded-lg ml-4 mr-4 px-4 py-1 text-sm text-black">
                                        <div className="flex justify-between items-center">
                                            <p className="text-lg">Semis</p>
                                            <div className="flex items-center justify-center bg-gray-200 text-center text-black text-sm px-4 py-1 rounded">
                                                {
                                                    loading ? (<Skeleton.Input active size="small" />) : (<span>{semis}</span>)
                                                }
                                            </div>
                                        </div>
                                    </div>
                                    <div className="border h-fit border-[#252963] rounded-lg ml-4 mr-4 px-4 py-1 text-sm text-black">
                                        <div className="flex justify-between items-center">
                                            <p className="text-lg">Beam Type</p>
                                            <div className="flex items-center justify-center bg-gray-200 text-center text-black text-sm px-4 py-1 rounded">
                                                {
                                                    loading ? (<Skeleton.Input active size="small" />) : (<span>{beamTypes}</span>)
                                                }
                                            </div>
                                        </div>
                                    </div>
                                    <div className="border h-fit border-[#252963] rounded-lg ml-4 mr-4 px-4 py-1 text-sm text-black">
                                        <div className="flex justify-between items-center">
                                            <p className="text-lg">Weight</p>
                                            <div className="flex items-center justify-center bg-gray-200 text-center text-black text-sm px-4 py-1 rounded">
                                                {
                                                    loading ? (<Skeleton.Input active size="small" />) : (<span>{weight}</span>)
                                                }
                                            </div>
                                        </div>
                                    </div>
                                    <div className="border h-fit border-[#252963] rounded-lg ml-4 mr-4 px-4 py-1 text-sm text-black">
                                        <div className="flex justify-between items-center">
                                            <p className="text-lg">Charging Type</p>
                                            <div className="flex items-center justify-center bg-gray-200 text-center text-black text-sm px-4 py-1 rounded">
                                                {
                                                    loading ? (<Skeleton.Input active size="small" />) : (<span>{chargingType}</span>)
                                                }
                                            </div>
                                        </div>
                                    </div>
                                    <div className="border h-fit border-[#252963] rounded-lg ml-4 mr-4 px-4 py-1 text-sm text-black">
                                        <div className="flex justify-between items-center">
                                            <p className="text-lg">Cast Grade</p>
                                            <div className="flex items-center justify-center bg-gray-200 text-center text-black text-sm px-4 py-1 rounded">
                                                {
                                                    loading ? (<Skeleton.Input active size="small" />) : (<span>{castGrade}</span>)
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold mb-4">Parameters</h3>
                                <div className="h-[400px] overflow-y-auto">
                                    {
                                        loading ? (<div className="grid grid-cols-3 p-2">
                                            {[...Array(9)].map((_, index) => (
                                                <div className="p-2 border border-gray-300">
                                                    <label className="block text-gray-700 font-semibold  mb-8"><Skeleton.Input active size="small" /></label>
                                                    <div className="mr-3">
                                                        <div className="mb-8">
                                                            <Skeleton.Input active size="large" />
                                                        </div>

                                                        <div className="text-center mt-1 text-sm text-gray-500">

                                                        </div>
                                                    </div>
                                                </div>

                                            ))}
                                        </div>) : (
                                            <div className="grid grid-cols-3 p-2 ">
                                                {pieceIdParameters.map((parameter: any, index: number) => {
                                                    const { column_name, value } = parameter;

                                                    const defaultRange = { min: 0, max: 1000, step: 1 };
                                                    const range = ColumnsRanges.find((item) => item.column_name === column_name) || defaultRange;

                                                    if (ColumnsRanges.filter((item: any) => item.hidden && item.hidden === true).map((item) => item.column_name.toLowerCase()).includes(column_name.toLowerCase())) {
                                                        return null; // Skip rendering for hidden columns
                                                    }

                                                    const { min, max, step } = range;
                                                    return (
                                                        <div
                                                            key={index}
                                                            className="p-4 overflow-hidden "
                                                        >
                                                            <div className="flex justify-between items-center w-full">
                                                                <label className="font-semibold sm:whitespace-nowrap mb-2 sm:mb-0 mr-2">
                                                                    {column_name}
                                                                </label>
                                                                <InputNumber
                                                                    min={min}
                                                                    max={max}
                                                                    style={{
                                                                        borderColor: (value < min || value > max) ? 'red' : '#4d507a',
                                                                        width: '30%',
                                                                        marginRight: '12px'

                                                                    }}
                                                                    value={value}
                                                                    onChange={(val) => handleSliderChange(index, val)}
                                                                />
                                                            </div>
                                                            <div className="flex-1 m-2">
                                                                <Slider
                                                                    value={value}
                                                                    step={step}
                                                                    min={min}
                                                                    max={max}
                                                                    marks={generateMarks(min, max, value)}
                                                                    onChange={(val) => handleSliderChange(index, val)}
                                                                    styles={{
                                                                        track: { background: 'transparent' },
                                                                        tracks: { background: '#4d507a' },
                                                                    }}
                                                                />
                                                            </div>
                                                        </div>
                                                    );
                                                })}
                                            </div>
                                        )
                                    }
                                </div>
                                <div className="flex gap-4 mt-4 mb-6">
                                    {/* <button className="border border-gray-300 btn-primary-new w-1/2 rounded-lg p-4 !text-base !h-10 text-center text-gray-500"
                                        onClick={handleSave}>
                                            Save Configuration
                                        </button> */}
                                    <button className="border border-gray-300 btn-primary-new w-full rounded-lg p-4 !text-base !h-10 text-center text-gray-500"
                                        onClick={handleExecute}>
                                        Execute
                                    </button>
                                </div>

                            </div>
                        </div>
                    </div>

                </div>
            ) : (
                <div>
                    {/* Charts and data section */}
                    <div className="border border-gray-300 rounded-lg p-4">
                        <div className="grid grid-cols-2 gap-4">
                            {/* Section 1 - SHAP Plot ( takes 2 rows ) */}
                            <div className="border border-gray-300 rounded-lg p-3 row-span-2">
                                {renderSHAPPlot()}
                            </div>
                            {/* Section 2 - Angular Gauge Chart */}
                            {/* <div className="border border-gray-300 rounded-lg p-3 flex items-center justify-center">
                  {renderGaugeChart()}
                </div> */}
                            {/* Section 3 - PDP Plot */}
                            {/* <div className="border border-gray-300 rounded-lg p-3">
                  {renderPDPPlot()}
                </div> */}
                            <div className="border border-gray-300 rounded-lg p-3">
                                {renderDecisionPlot()}
                            </div>
                            {/* <div className="border border-gray-300 rounded-lg p-3 col-span-2">
                                {renderForcePlot()}
                            </div> */}
                        </div>
                    </div>

                    {/* Download Button */}
                    <Button
                        className="border border-gray-300 btn-primary-new w-full mt-4 mb-6 rounded-lg !text-base !h-10 text-center text-gray-500"
                        icon={<DownloadOutlined />}
                        onClick={handleDownloadPPT}
                    >
                        Download Options
                    </Button>
                </div>
            )
            }

        </div>
    );
};

export default PredictionModelChart;
