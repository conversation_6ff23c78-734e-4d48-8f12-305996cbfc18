import React, { useState, useEffect, useRef } from "react";
import { Button, Input, Select, Form, Radio, InputNumber, Space } from "antd";
import { useLocation } from "react-router-dom";
import { useGoldenValues } from "../../context/goldenValues";
import { useSelector } from 'react-redux';
import { getRequest } from "../../utils/apiHandler";
import { Slider, SliderSingleProps } from 'antd';
import { Skeleton } from 'antd';
import { ColumnsRanges } from "../../utils/predictionModelColumnsRange";
const { Option } = Select;

interface OperationConfigProps {
    nodeData?: any;
    operationType?: string;
    selectedGoldenName: any;
    selectedGoldenId: any;
    onSave?: (config: any) => void;
    setPreSetQueryConfigurations: any;
    preSetOperationConfigurations: any;
    setGoldenFilterId: any;
    csvId?: any;
    onChange?: (config: any) => void
}

function OperationConfig({ nodeData, operationType, selectedGoldenName, selectedGoldenId, onSave, setPreSetQueryConfigurations, preSetOperationConfigurations, setGoldenFilterId, csvId, onChange }: OperationConfigProps) {
    // console.log('csvId !!!!!!!!!!!!!!!!!!!!!!!!!!!!!:', csvId);
    // console.log('operationType @@@@@@@@@@@@@@@@@@@@@@@@@@@:', operationType);
    const { goldenBatches } = useSelector((state: any) => state.goldenBatch);
    const [clusteringModel, setClusteringModel] = useState("kmeans");
    const [correlationType, setCorrelationType] = useState({
        cross_correlation: "false",
        // target_variable_correlation: "true"
    });
    const [rcaRuns, setRcaRuns] = useState(true);
    const [theresold, setTheresold] = useState(0);
    const [selectGoldenName, setselectGoldenName] = useState(null);
    const [selectCustomGoldenName, setSelectCustomGoldenName] = useState<any>(null);
    const [customGoldenData, setCustomGoldenData] = useState<any>(null);
    const [goldenValueData, setGoldenValueData] = useState(null);
    const [customFilterDropdown, setCustomFilterDropdown] = useState([]);
    const [goldenParameters, setGoldenParameters] = useState({ trueValues: [], falseValues: [] });
    const { goldenValue, updateGoldenValue } = useGoldenValues();
    const [dimensionalityReduction, setDimensionalityReduction] = useState<any>(null)
    const [pieceIds, setPieceIds] = useState<any>([])
    const [pieceIdParameters, setPieceIdParameters] = useState<any>([])
    const [selectedPieceId, setSelectedPieceId] = useState<any>([])
    // const [pieceIdRowData , setPieceIdRowData] = useState<any>([])
    const [section, setSection] = useState(null)
    const [semis, setSemis] = useState(null)
    const [beamTypes, setBeamTypes] = useState<string | null>(null)
    const [weight, setWeight] = useState<string | null>(null)
    const [chargingType, setChargingType] = useState<string | null>(null)
    const [castGrade, setCastGrade] = useState<string | null>(null)
    const [loading, setLoading] = useState<boolean>(false)
    const location = useLocation()
    const params = new URLSearchParams(location.search);
    const executeFlow = params.get('execute');
    // Initialize states from saved configuration
    useEffect(() => {
        if (nodeData?.data?.operationConfig) {
            if (nodeData.data.operationConfig.correlation) {
                setCorrelationType({
                    cross_correlation: nodeData.data.operationConfig.correlation.cross_correlation || "False",
                    // target_variable_correlation: nodeData.data.operationConfig.correlation.target_variable_correlation || "True"
                });
            }
            if (nodeData.data.operationConfig.golden_cluster) {
                setClusteringModel(nodeData.data.operationConfig.golden_cluster.clustering_model || "kmean");
                if (nodeData.data.operationConfig.rca) {
                    setRcaRuns(nodeData.data.operationConfig.rca.rca_runs || true);
                }
            }
            if (nodeData.data.operationConfig.dimensionality_reduction) {
                setDimensionalityReduction(nodeData.data.operationConfig.dimensionality_reduction.algorithm || "kmean")
            }
            if (nodeData.data.operationConfig.rca) {
                setRcaRuns(nodeData.data.operationConfig.rca.rca_runs || true);
                setTheresold(nodeData.data.operationConfig.rca.deviation_threshold || 0);
                setselectGoldenName(nodeData.data.operationConfig.rca.name || null);
                setSelectCustomGoldenName(nodeData?.data?.operationConfig?.rca?.custom_name || null);
                setGoldenValueData(nodeData.data.operationConfig.rca.id || null);
                const excludedFeatures = nodeData?.data?.operationConfig?.golden_cluster?.exclude_features || [];
                // Get all available columns
                const allColumns = preSetOperationConfigurations || [];
                // Set selected columns as those that are NOT in exclude_features
                const selectedCols = allColumns?.filter((col: any) => !excludedFeatures.includes(col));
                setSelectedColumns(selectedCols);
            }
            if (nodeData.data.operationConfig.predictionModel) {
                setPieceIdParameters(nodeData.data.operationConfig.predictionModel.pieceIdParameters)
                setSection(nodeData.data.operationConfig.predictionModel.section)
                setSemis(nodeData.data.operationConfig.predictionModel.semis)
                setBeamTypes(nodeData.data.operationConfig.predictionModel.beamTypes)
                setWeight(nodeData.data.operationConfig.predictionModel.weight)
                setChargingType(nodeData.data.operationConfig.predictionModel.chargingType)
                setCastGrade(nodeData.data.operationConfig.predictionModel.castGrade)
                setSelectedPieceId(nodeData.data.operationConfig.predictionModel.pieceId)
            }
        }
    }, [nodeData]);

    useEffect(() => {
        if (selectedGoldenName) {
            setselectGoldenName(selectedGoldenName);
        }
        if (selectedGoldenId) {
            setGoldenValueData(selectedGoldenId);
        }
    }, [selectedGoldenName, selectedGoldenId]);


    useEffect(() => {
        if (operationType == 'prediction_model' && csvId) {
            getPieceIds()
        } else {
            setPieceIds([])
            setSection(null)
            setSemis(null)
            setBeamTypes(null)
            setWeight(null)
            setChargingType(null)
            setCastGrade(null)
        }
    }, [csvId]);

    const getPieceIds = async () => {
        try {
            const response = await getRequest(`/file/piece-ids/${csvId}`)
            if (response.status == 200) {
                setPieceIds(response?.data?.data?.pieceIds)
                handlePieceIDChange(response?.data?.data?.pieceIds[0])
            } else {
                setPieceIds([])
                // setPieceIdRowData([])
                setPieceIdParameters([])
            }

        } catch (error) {
            console.log('error :', error);
        }
    }

    const filterAndConvertToNumbers = (data: any[]): any[] => {
        const validColumns = new Set(ColumnsRanges.map(col => col.column_name.toLowerCase()));

        return data
            .filter(item => {
                const num = Number(item.value);
                return (
                    !isNaN(num) &&
                    isFinite(num) &&
                    item.column_name.toLowerCase() !== "piece id" &&
                    validColumns.has(item.column_name.toLowerCase())
                );
            })
            .map(item => ({
                column_name: item.column_name,
                value: Number(item.value)
            }));
    };

    const handlePieceIDChange = async (option: any) => {
        try {
            setLoading(true)
            setSelectedPieceId(option)
            const response = await getRequest(`/file/pieceId-rowdata/${option}/${csvId}`)
            console.log('response :', response);
            if (response.status == 200) {
                const beamTypesList = [
                    'IPE',
                    'HEA',
                    'HEB',
                    'HEM',
                    'W',
                    'UB',
                    'UC',
                    'HP',
                    'UPN',
                    'UPSL',
                    'NPB',
                    'WPB',
                    'H',
                ];

                // setPieceIdRowData(response?.data?.data?.rowData)
                const section = response?.data?.data?.rowData.find((item: { column_name: string; }) => item.column_name.toLowerCase() == "section")?.value
                const semis = response?.data?.data?.rowData.find((item: { column_name: string; }) => item.column_name.toLowerCase() == "semis")?.value
                const castGrade = response?.data?.data?.rowData.find((item: { column_name: string; }) => item.column_name.toLowerCase() == "cast grade")?.value
                const beamTypes = beamTypesList.find(b => String(section).includes(b)) || 'Unknown';
                const weight = ((w => isNaN(w) ? null : w)(Number(String(section).split('_').pop())));
                const chargingTemp = response?.data?.data?.rowData.find((item: { column_name: string; }) => item.column_name.toLowerCase() == "charging temp")?.value
                const chargingType = chargingTemp < 200 ? 'Cold Charge' : 'Hot Charge'

                setSection(section)
                setSemis(semis)
                setBeamTypes(beamTypes)
                setWeight(String(weight))
                setChargingType(chargingType)
                setCastGrade(castGrade)
                const rowData = await filterAndConvertToNumbers(response?.data?.data?.rowData)
                console.log('rowData :', rowData);
                setPieceIdParameters(rowData);
                setLoading(false)
            } else {
                setPieceIdParameters([])
                setSection(null)
                setSemis(null)
                setBeamTypes(null)
                setWeight(null)
                setChargingType(null)
                setCastGrade(null)
                setLoading(false)
            }

        } catch (error) {
            console.log('error :', error);
            setLoading(false)
        }
    }

    const handleSliderChange = (index: number, newValue: number) => {
        // Update the value of the pieceIdParameters array at the specific index
        if (!newValue) return
        setPieceIdParameters((prevParameters: any) => {
            const updatedParameters = [...prevParameters];
            updatedParameters[index] = {
                ...updatedParameters[index],
                value: newValue // Update the specific parameter's value
            };
            return updatedParameters;
        });
    };

    const handleDropdownValue = (value: any) => {
        const parsedValue = JSON.parse(value);
        setSelectCustomGoldenName(null)
        setGoldenValueData(parsedValue.id)
        // let path_for_aiml = parsedValue.settings.datasource.file_path.split('DATA/')
        // let file_path = 'public/datasource/' + path_for_aiml[1];
        setselectGoldenName(parsedValue.name);
        let path_for_aiml = parsedValue?.settings?.datasource?.file_path?.split('DATA/')
        let file_path = path_for_aiml ? 'public/datasource/' + path_for_aiml[1] : '';
        updateGoldenValue(file_path)
        setPreSetQueryConfigurations(parsedValue?.settings?.datasource)

        if (parsedValue.filter_id) {
            setGoldenFilterId(parsedValue.filter_id)
            const filteredTrueValues = goldenParameters.trueValues.filter((trueValue: any) => {
                return trueValue.filter_id === parsedValue.filter_id;
            });
            //   setGoldenParameters((prev) => ({
            //     ...prev,
            //     trueValues: filteredTrueValues,
            // }));
            setCustomFilterDropdown(filteredTrueValues)
        }
        else
            setCustomFilterDropdown([])

    };

    const handleCustomDropdownValue = (value: any) => {
        const parsedValue = JSON.parse(value);
        console.log('parsedValue', parsedValue)
        setSelectCustomGoldenName(parsedValue.name)
        setCustomGoldenData(parsedValue)
    };

    const [selectedColumns, setSelectedColumns] = useState<string[]>(preSetOperationConfigurations);


    // Coment useEffect for persistance of selected columns
    // useEffect(() => {
    //   // Initialize selected columns when preSetOperationConfigurations changes
    //   if (preSetOperationConfigurations) {
    //     setSelectedColumns(preSetOperationConfigurations);
    //   }
    // }, [preSetOperationConfigurations]);

    const handleColumnToggle = (columnName: string, isChecked: boolean) => {
        setSelectedColumns(prev => {
            if (isChecked) {
                return [...prev, columnName];
            } else {
                return prev.filter(col => col !== columnName);
            }
        });
    };

    const createChunkedFields = (fields: string[]): string[][] => {
        return fields?.reduce((acc: string[][], curr: string, i: number) => {
            if (i % 3 === 0) {
                acc.push([curr]);
            } else {
                acc[acc.length - 1].push(curr);
            }
            return acc;
        }, []) || [];
    };

    useEffect(() => {
        getGoldenParameters();
    }, [])

    const getGoldenParameters = async () => {
        try {
            let filters = {
                pageSize: 500
            }
            const response = await getRequest(`/golden-data/0`, { filters });
            console.log('response', response)
            if (response?.data?.data) {
                const trueValues = response.data.data.filter((item: any) => item.custom_parameter == true);
                console.log('trueValues', trueValues)
                const falseValues = response.data.data.filter((item: any) => item.custom_parameter == false || !item.custom_parameter);
                console.log('falseValues', falseValues)
                setGoldenParameters({ trueValues, falseValues })

                if (nodeData?.data?.operationConfig?.rca?.id) {
                    let filterId = falseValues.filter((item: any) => item.id == nodeData?.data?.operationConfig?.rca?.id)
                    filterId = filterId[0].filter_id
                    if (filterId) {
                        const filteredTrueValues = trueValues.filter((trueValue: any) => {
                            return trueValue.filter_id === filterId;
                        });
                        setCustomFilterDropdown(filteredTrueValues)
                    }
                }
            }
        } catch (error) {
            console.log('error', error)
        }
    }

    const generateMarks = (min: number, max: number, current: number): SliderSingleProps['marks'] => {
        return {
            [min]: <span className="slider-min-max">{min}</span>,
            // [current]: <strong className="slider-current-value">{current}</strong>,
            [max]: <span className="slider-min-max">{max}</span>,
        };
    };
    const renderOperationUI = () => {
        switch (operationType) {
            case 'golden_batch':
                return (
                    <>
                        {/* Column Selection Section */}
                        <h3 className='mb-3'>Select Columns</h3>
                        {preSetOperationConfigurations ? (<div className="mb-4 border p-4 rounded">
                            {createChunkedFields(preSetOperationConfigurations || []).map((row: string[], rowIndex: number) => (
                                <div key={rowIndex} className="flex mb-2">
                                    {row.map((field: string) => (
                                        <div key={field} className="w-1/3 flex items-center">
                                            <input
                                                type="checkbox"
                                                id={field}
                                                checked={selectedColumns.includes(field)}
                                                onChange={(e) => handleColumnToggle(field, e.target.checked)}
                                                disabled={!!executeFlow}
                                                className="mr-2 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                            />
                                            <label htmlFor={field} className="text-sm text-gray-700 cursor-pointer">
                                                {field}
                                            </label>
                                        </div>
                                    ))}
                                </div>
                            ))}
                        </div>) : (<div className="mb-4 border p-4 rounded">Please save CSV settings before proceeding.</div>)}

                        {/* Existing Golden Batch Configuration */}
                        <Form.Item label="Clustering Model">
                            <Select
                                value={clusteringModel}
                                onChange={(value) => setClusteringModel(value)}
                                disabled={!!executeFlow}
                            >
                                <Option value="kmean">K-means</Option>
                            </Select>
                        </Form.Item>
                        <Form.Item label="RCA Runs">
                            <Select
                                value={rcaRuns}
                                onChange={(value) => setRcaRuns(value)}
                                disabled={!!executeFlow}
                            >
                                <Option value='true'>True</Option>
                                <Option value='false'>False</Option>
                            </Select>
                        </Form.Item>
                        <Form.Item label="Deviation Threshold">
                            <Space>
                                <InputNumber<number>
                                    defaultValue={0}
                                    min={0}
                                    max={100}
                                    formatter={(value) => `${value}%`}
                                    value={theresold}
                                    disabled={!!executeFlow}
                                    parser={(value) => value?.replace('%', '') as unknown as number}
                                    onChange={(value) => setTheresold(value ? value : 0)}
                                />
                            </Space>
                        </Form.Item>
                    </>
                );
            case 'correlation':
                return (
                    <>
                        <Form.Item label="Cross Correlation">
                            <Radio.Group
                                value={correlationType.cross_correlation}
                                onChange={(e) => setCorrelationType(prev => ({
                                    ...prev,
                                    cross_correlation: e.target.value
                                }))}
                                disabled={!!executeFlow}
                            >
                                <Radio value="true">True</Radio>
                                <Radio value="false">False</Radio>
                            </Radio.Group>
                        </Form.Item>
                        {/* <Form.Item label="Target Variable Correlation">
              <Radio.Group
                value={correlationType.target_variable_correlation}
                onChange={(e) => setCorrelationType(prev => ({
                  ...prev,
                  target_variable_correlation: e.target.value
                }))}
              >
                <Radio value="true">True</Radio>
                <Radio value="false">False</Radio>
              </Radio.Group>
            </Form.Item> */}
                    </>
                );
            case 'rca':
                return (
                    <div>
                        {/* <Form.Item label="RCA Runs">
            <Select
              value={rcaRuns}
              onChange={(value) => setRcaRuns(value)}
            >
              <Option value="true">True</Option>
              <Option value="false">False</Option>
            </Select>
          </Form.Item> */}
                        {/* <Select
              value={selectGoldenName}
              onChange={(option) => handleDropdownValue(option)}
              style={{ width: 200, marginBottom: 10 }}
              placeholder="Select Golden Name"
            >
              {goldenBatches?.map((option: any, index: any) => (
                <Option key={index} value={JSON.stringify(option)}>
                  {option.name}
                </Option>
              ))}
            </Select> */}
                        <div className="inline-flex flex-col">
                            <label>Golden Name</label>
                            <Select
                                value={selectGoldenName}
                                onChange={(option) => handleDropdownValue(option)}
                                style={{ width: 200, marginBottom: 10 }}
                                placeholder="Select Golden Name"
                            >
                                {goldenParameters.falseValues?.map((option: any, index: any) => (
                                    <Option key={index} value={JSON.stringify(option)}>
                                        {option.name}
                                    </Option>
                                ))}
                            </Select>
                        </div>
                        {customFilterDropdown?.length > 0 && (
                            <div className="inline-flex flex-col ml-3">
                                <label>Custom Golden Name</label>
                                <Select
                                    value={selectCustomGoldenName}
                                    onChange={(option) => handleCustomDropdownValue(option)}
                                    style={{ width: 200, marginBottom: 10 }}
                                    placeholder="Select Custom Golden Name"
                                    className=""
                                >
                                    {customFilterDropdown?.map((option: any, index: any) => (
                                        <Option key={index} value={JSON.stringify(option)}>
                                            {option.name}
                                        </Option>
                                    ))}
                                </Select>
                            </div>
                        )}
                        <Form.Item label="Deviation Threshold">
                            <Space>
                                <InputNumber<number>
                                    defaultValue={0}
                                    min={0}
                                    max={100}
                                    formatter={(value) => `${value}%`}
                                    value={theresold}
                                    disabled={!!executeFlow}
                                    parser={(value) => value?.replace('%', '') as unknown as number}
                                    onChange={(value) => setTheresold(value ? value : 0)}
                                />
                            </Space>
                        </Form.Item>
                    </div>
                );
            case 'feature_importance':
                return <div>Feature Importance UI coming soon</div>;
            case 'prediction_model':
                return <>
                    <div className="mb-2">
                        <div className="bg-blue-10 p-8 rounded-lg shadow-md w-full mx-auto">
                            <h2 className="text-2xl font-bold text-center mb-2">Prediction Settings</h2>
                            <hr className="border-t border-gray-400 mb-6 w-full" />

                            <div className="mb-8">
                                <h3 className="text-lg font-semibold mb-4">Filters</h3>
                                <div className="mb-4 p-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-2">
                                            Select Piece ID
                                        </label>
                                        <Select
                                            placeholder="Select Piece ID"
                                            style={{ width: '100%' }}
                                            value={selectedPieceId || undefined}
                                            onChange={(option) => handlePieceIDChange(option)}
                                            options={pieceIds.map((pieceId: any) => ({
                                                value: pieceId,
                                                label: pieceId
                                            }))}
                                        />
                                    </div>
                                </div>
                                <div className="grid grid-cols-3 sm:grid-cols-3 md:grid-cols-3 gap-4">
                                    <div className="border h-fit border-[#252963] rounded-lg ml-4 mr-4 px-4 py-1 text-sm text-black">
                                        <div className="flex justify-between items-center">
                                            <p className="text-lg">Section</p>
                                            <div className="flex items-center justify-center bg-gray-200 text-center text-black text-sm px-4 py-1 rounded overflow-hidden">
                                                {loading ? (
                                                    <Skeleton.Input active size="small" />
                                                ) : (
                                                    <span className="text-sm">{section}</span>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                    <div className="border h-fit border-[#252963] rounded-lg ml-4 mr-4 px-4 py-1 text-sm text-black">
                                        <div className="flex justify-between items-center">
                                            <p className="text-lg">Semis</p>
                                            <div className="flex items-center justify-center bg-gray-200 text-center text-black text-sm px-4 py-1 rounded">
                                                {
                                                    loading ? (<Skeleton.Input active size="small" />) : (<span>{semis}</span>)
                                                }
                                            </div>
                                        </div>
                                    </div>
                                    <div className="border h-fit border-[#252963] rounded-lg ml-4 mr-4 px-4 py-1 text-sm text-black">
                                        <div className="flex justify-between items-center">
                                            <p className="text-lg">Beam Type</p>
                                            <div className="flex items-center justify-center bg-gray-200 text-center text-black text-sm px-4 py-1 rounded">
                                                {
                                                    loading ? (<Skeleton.Input active size="small" />) : (<span>{beamTypes}</span>)
                                                }
                                            </div>
                                        </div>
                                    </div>
                                    <div className="border h-fit border-[#252963] rounded-lg ml-4 mr-4 px-4 py-1 text-sm text-black">
                                        <div className="flex justify-between items-center">
                                            <p className="text-lg">Weight</p>
                                            <div className="flex items-center justify-center bg-gray-200 text-center text-black text-sm px-4 py-1 rounded">
                                                {
                                                    loading ? (<Skeleton.Input active size="small" />) : (<span>{weight}</span>)
                                                }
                                            </div>
                                        </div>
                                    </div>
                                    <div className="border h-fit border-[#252963] rounded-lg ml-4 mr-4 px-4 py-1 text-sm text-black">
                                        <div className="flex justify-between items-center">
                                            <p className="text-lg">Charging Type</p>
                                            <div className="flex items-center justify-center bg-gray-200 text-center text-black text-sm px-4 py-1 rounded">
                                                {
                                                    loading ? (<Skeleton.Input active size="small" />) : (<span>{chargingType}</span>)
                                                }
                                            </div>
                                        </div>
                                    </div>
                                    <div className="border h-fit border-[#252963] rounded-lg ml-4 mr-4 px-4 py-1 text-sm text-black">
                                        <div className="flex justify-between items-center">
                                            <p className="text-lg">Cast Grade</p>
                                            <div className="flex items-center justify-center bg-gray-200 text-center text-black text-sm px-4 py-1 rounded">
                                                {
                                                    loading ? (<Skeleton.Input active size="small" />) : (<span>{castGrade}</span>)
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold mb-4">Parameters</h3>
                                <div className="h-[400px] overflow-y-auto">
                                    {
                                        loading ? (<div className="grid grid-cols-3 p-2">
                                            {[...Array(9)].map((_, index) => (
                                                <div className="p-2 border border-gray-300">
                                                    <label className="block text-gray-700 font-semibold  mb-8"><Skeleton.Input active size="small" /></label>
                                                    <div className="mr-3">
                                                        <div className="mb-8">
                                                            <Skeleton.Input active size="large" />
                                                        </div>

                                                        <div className="text-center mt-1 text-sm text-gray-500">

                                                        </div>
                                                    </div>
                                                </div>

                                            ))}
                                        </div>) : (
                                            <div className="grid grid-cols-3 p-2 ">
                                                {pieceIdParameters.map((parameter: any, index: number) => {
                                                    const { column_name, value } = parameter;
                                                    const defaultRange = { min: 0, max: 1000, step: 1 };
                                                    const range = ColumnsRanges.find((item) => item.column_name === column_name) || defaultRange;

                                                    if ( ColumnsRanges.filter((item: any) => item.hidden && item.hidden === true).map((item) => item.column_name.toLowerCase()).includes(column_name.toLowerCase())) {
                                                        return null; // Skip rendering for hidden columns
                                                    }

                                                    const { min, max, step } = range;
                                                    return (
                                                        <div
                                                            key={index}
                                                            className="p-4 overflow-hidden "
                                                        >
                                                            <div className="flex justify-between items-center w-full">
                                                                <label className="font-semibold sm:whitespace-nowrap mb-2 sm:mb-0 mr-2">
                                                                    {column_name}
                                                                </label>
                                                                <InputNumber
                                                                    min={min}
                                                                    max={max}
                                                                    style={{
                                                                        borderColor: (value < min || value > max) ? 'red' : '#4d507a',
                                                                        width: '30%',
                                                                        marginRight: '12px'

                                                                    }}
                                                                    value={value}
                                                                    onChange={(val) => handleSliderChange(index, val)}
                                                                />
                                                            </div>
                                                            <div className="flex-1 m-2">
                                                                <Slider
                                                                    value={value}
                                                                    step={step}
                                                                    min={min}
                                                                    max={max}
                                                                    marks={generateMarks(min, max, value)}
                                                                    onChange={(val) => handleSliderChange(index, val)}
                                                                    styles={{
                                                                        track: { background: 'transparent' },
                                                                        tracks: { background: '#4d507a' },
                                                                    }}
                                                                />
                                                                {/* <div className="flex-1">
                                                                    <div className="flex items-center gap-4 justify-center">
                                                                    <InputNumber
                                                                        min={min}
                                                                        max={max}
                                                                        style={{
                                                                        borderColor: '#4d507a',
                                                                        width: '25%',
                                                                        }}
                                                                        value={value}
                                                                        onChange={(val) => handleSliderChange(index, val)}
                                                                    />
                                                                    <div className="flex items-center justify-center bg-gray-200 text-center text-black text-sm px-4 py-2 rounded">
                                                                        <span className="mr-3">Min : {min} </span>
                                                                        -
                                                                        <span className="ml-3">Max : {max} </span>
                                                                    </div>
                                                                    </div>
                                                                </div> */}
                                                            </div>
                                                        </div>
                                                    );
                                                })}
                                            </div>
                                        )
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </>
            case 'identification_model':
                return (
                    <>
                        {/* Column Selection Section */}
                        {/* <h3 className='mb-3'>Select Columns</h3>
            {preSetOperationConfigurations?(<div className="mb-4 border p-4 rounded">
              {createChunkedFields(preSetOperationConfigurations || []).map((row: string[], rowIndex: number) => (
                <div key={rowIndex} className="flex mb-2">
                  {row.map((field: string) => (
                    <div key={field} className="w-1/3 flex items-center">
                      <input
                        type="checkbox"
                        id={field}
                        checked={selectedColumns.includes(field)}
                        onChange={(e) => handleColumnToggle(field, e.target.checked)}
                        disabled={!!executeFlow}
                        className="mr-2 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <label htmlFor={field} className="text-sm text-gray-700 cursor-pointer">
                        {field}
                      </label>
                    </div>
                  ))}
                </div>
              ))}
            </div>):(<div className="mb-4 border p-4 rounded">Please save CSV settings before proceeding.</div>)} */}
                        <Form.Item label="Clustering Model">
                            <Select
                                value={clusteringModel}
                                onChange={(value) => setClusteringModel(value)}
                            // disabled={!!executeFlow}
                            >
                                <Option value="kmean">K-means</Option>
                            </Select>
                        </Form.Item>
                        <Form.Item label="Dimensionality Reduction">
                            <Select
                                value={dimensionalityReduction}
                                onChange={(value) => setDimensionalityReduction(value)}
                            // disabled={!!executeFlow}
                            >
                                <Option value="tsne">tsne</Option>
                                <Option value="pca">pca</Option>
                                {/* <Option value='false'>False</Option> */}
                            </Select>
                        </Form.Item>
                        {/* <Form.Item label="Deviation Threshold">
              <Space>
                <InputNumber<number>
                  defaultValue={0}
                  min={0}
                  max={100}
                  formatter={(value) => `${value}%`}
                  value={theresold}
                  disabled={!!executeFlow}
                  parser={(value) => value?.replace('%', '') as unknown as number}
                  onChange={(value) => setTheresold(value ? value : 0)}
                />
              </Space>
            </Form.Item> */}
                    </>
                );
            default:
                return <div>Select an operation type</div>;
        }
    };

    const handleSaveConfig = () => {
        let configData;

        switch (operationType) {
            case 'golden_batch':
                const allColumns = preSetOperationConfigurations || [];
                // Get unchecked columns (those not in selectedColumns)
                const excludedColumns = allColumns.filter((col: any) => !selectedColumns.includes(col));
                configData = {
                    golden_cluster: {
                        exclude_features: excludedColumns,
                        clustering_model: clusteringModel
                    },
                    rca: {
                        rca_runs: rcaRuns,
                        deviation_threshold: theresold
                    }
                };
                break;
            case 'correlation':
                configData = {
                    correlation: {
                        cross_correlation: correlationType.cross_correlation,
                        // target_variable_correlation: correlationType.target_variable_correlation
                    }
                };
                break;
            case 'rca':
                configData = {
                    rca: {
                        rca_runs: rcaRuns,
                        deviation_threshold: theresold,
                        cluster_run_data: goldenValueData,
                        name: selectGoldenName,
                        id: goldenValueData,

                        ...(customGoldenData && {
                            custom_rca_runs: rcaRuns,
                            custom_deviation_threshold: theresold,
                            custom_cluster_run_data: customGoldenData.id,
                            custom_name: customGoldenData.name,
                            custom_id: customGoldenData.id,
                        })
                    }
                };
                break;
            case 'identification_model':
                configData = {
                    clustering: {
                        algorithm: clusteringModel,
                        config: {}
                    },
                    dimensionality_reduction: {
                        algorithm: dimensionalityReduction,
                        config: {}
                    }
                };
                break;
            case 'prediction_model':
                configData = {
                    predictionModel: {
                        pieceIdParameters: pieceIdParameters,
                        section: section,
                        semis: semis,
                        beamTypes: beamTypes,
                        pieceId: selectedPieceId,
                        weight: weight,
                        castGrade: castGrade,
                        chargingType: chargingType,
                    }
                };
                break;
            default:
                configData = {};
        }

        console.log("Saving config:", configData);
        onSave?.(configData);
    };
    const triggerOnChange = () => {
        const configData = {
            rca: {
                rca_runs: rcaRuns,
                deviation_threshold: theresold,
                cluster_run_data: goldenValueData,
                name: selectGoldenName,
                id: goldenValueData,

                ...(customGoldenData && {
                    custom_rca_runs: rcaRuns,
                    custom_deviation_threshold: theresold,
                    custom_cluster_run_data: customGoldenData.id,
                    custom_name: customGoldenData.name,
                    custom_id: customGoldenData.id,
                })
            }
        };
        onChange?.(configData)
    }
    return (
        <div className="p-5 rounded-lg">
            <h3 className="mb-2">Operation Configuration:</h3>
            <Form layout="vertical" className="operation-form">
                {renderOperationUI()}
                <Form.Item>
                    {!executeFlow && (
                        <button className="btn btn-primary-new" onClick={handleSaveConfig}>
                            Save Configuration
                        </button>
                    )}
                    <button className="btn btn-primary-new invisible" id="save-workflow-config" onClick={triggerOnChange} >
                        Save Configuration
                    </button>
                </Form.Item>
            </Form>
        </div>
    );
}

export default OperationConfig;
