import { useEffect, useState } from "react";
import { deleteRequest, getRequest, postRequest } from "../../utils/apiHandler";
import FolderModal from "../Modal/FolderModal";
import FileModal from "../Modal/FileModal";
import { useNavigate } from "react-router-dom";
import OpenFolder from "../../img/folderOpen-icon.svg";
import CloseFolder from "../../img/folder-icon.svg";
import AddFolder from "../../img/Add-Folder-icon.svg";
import DeleteIcon from "../../img/trash-icon.svg";
import RunIcon from "../../img/run-icon.png";
import ExecuteIcon from "../../img/execute-icon.png";
import AddFile from "../../img/Add-File-icon.svg";
import ConfirmDeleteModal from "../Modal/ConfirmDeleteModal";
import Notiflix from "notiflix";
import { useSelector, useDispatch } from 'react-redux';
import shareIcon from "../../img/share.svg"
import TaskStatusModal from "../Modal/shareWorkflowModal";
import { useAuth } from "../../context/AuthContext";
import type { CollapseProps } from 'antd';
import { Button, Collapse, Flex } from 'antd';
import { message } from "antd";
type Node = {
  id: number;
  name: string;
  type: "folder" | "file";
  isOpen?: boolean;
  children?: Node[];
  access_level?: any;
  share_type?: any;
  flowType?: any
};

interface FolderProps {
  node: Node;
  setViewTabOpen?: (val: boolean) => void;
  onAdd: (parentId: number, type: "folder" | "file", name: string, flowType?: string) => void;
  onToggle: (id: number) => void;
  onDelete: (id: number, type: "folder" | "file") => void;
}

const initialStructure: Node[] = [
  // {
  //   id: 1,
  //   name: "Folder 1",
  //   type: "folder",
  //   isOpen: false,
  //   children: [
  //     { id: 2, name: "Folder 1.1", type: "folder", isOpen: false, children: [] },
  //     { id: 3, name: "Folder 1.2", type: "folder", isOpen: false, children: [] },
  //     { id: 4, name: "Workflow 1", type: "file" },
  //   ],
  // },
];

const Folder: React.FC<FolderProps> = ({ node, setViewTabOpen, onAdd, onToggle, onDelete }) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [showConfirmDeleteModel, setShowConfirmDeleteModel] = useState(false);
  const [isFileModalVisible, setIsFileModalVisible] = useState(false);
  const navigate = useNavigate();
  const [selectedNode, setSelectedNode] = useState<Node | null>(null);
  const [openShareModal, setOpenShareModal] = useState<boolean>(false)
  const { authState } = useAuth();
  const handleCreateNewWorkflow = (id: Number) => {
    navigate('/?tab=insight&workflowId=' + id);
  };
  const handleCreateNewView = (id: Number) => {
    navigate('/?tab=insight&viewId=' + id);

    if (setViewTabOpen) {
      setViewTabOpen(false)
    }
  }

  const showDeleteConfirmModel = (node: any) => {
    setShowConfirmDeleteModel(true);
    setSelectedNode(node);

  };

  const handleDeleteConfirm = async () => {
    try {
      if (selectedNode) {
        let endPoint = selectedNode?.flowType && selectedNode?.flowType == 'view' ? 'view' : 'workflow'
        const response = await deleteRequest(`/${endPoint}/${selectedNode?.id}/${selectedNode?.type}`);
        if (response.status === 200) {
          // Notiflix.Notify.success('Workflow deleted successfully');
          message.success(`${endPoint} deleted successfully`);
          setShowConfirmDeleteModel(false);
          onDelete(selectedNode?.id, selectedNode?.type);

        }
      }

    } catch (error) {
      console.error('Error deleting workflow:', error);
    }
  };

  const handleDeleteCancel = () => {
    setShowConfirmDeleteModel(false);
  };

  const handleWorkflowRuns = (workflowId: any) => {
    navigate(`/?tab=insight&workflowId=${workflowId}&runsListing=true`)
  }

  const handleExecuteButton = (workflowId: any) => {
    navigate(`/?tab=insight&workflowId=${workflowId}&execute=true`)
  }

  return (
    <div className="ml-6">
      {node.type === "folder" && (
        <div className="relative">
          <span
            className="cursor-pointer flex items-center space-x-2 after:w-[1px] after:h-[calc(100%-64px)] after:bg-[#d7d7d7] after:left-[8px] after:top-[26px] after:absolute"
          // onClick={() => onToggle(node.id)}
          >
            {/* <span>{node.isOpen ? "📂" : "📁"}</span> */}
            <span>{node.isOpen ? <img src={OpenFolder} alt="Open Folder" className="w-4 h-4" /> : <img src={CloseFolder} alt="Close Folder" className="w-4 h-4" />}</span>
            <span className="text-black font-medium items-center flex w-full folder-name">
              <span className="w-full overflow-hidden whitespace-nowrap text-ellipsis" onClick={() => onToggle(node.id)}> {node.name} </span>
              {node?.id != 0 && (
                <div className="ms-auto flex space-x-2 icons">
                  <button
                    className=""
                    title="Add Folder"
                    onClick={() => setIsModalVisible(true)}
                  >
                    {/* Add Folder */}
                    <img src={AddFolder} alt="Add Folder" className="min-w-4 min-h-3 w-4 h-4" />
                  </button>
                  <button
                    title="Add Workflow"
                    onClick={() => setIsFileModalVisible(true)}
                  >
                    {/* Add File */}
                    <img src={AddFile} alt="Add Workflow" className="min-w-4 min-h-3 w-4 h-4" />
                  </button>
                  <button title="Delete" onClick={() => showDeleteConfirmModel(node)}>
                    {/* 🗑️ */}

                    <img src={DeleteIcon} alt="Delete" className="min-w-4 min-h-3 w-4 h-4" />

                  </button>
                </div>
              )}
            </span>
          </span>
          {node.isOpen && (
            <div className="mt-2">
              {node.children?.map((child) => (
                <Folder
                  key={child.id}
                  setViewTabOpen={setViewTabOpen}
                  node={child}
                  onAdd={onAdd}
                  onToggle={onToggle}
                  onDelete={onDelete}
                />
              ))}
              {/* <div className="flex space-x-2 mt-2">
                <button
                  className="bg-primary text-white px-3 py-1 text-sm rounded"
                  onClick={() => setIsModalVisible(true)}
                >
                  <img src={AddFolder} alt="Add Folder" className="w-4 h-4" />
                </button>
                <button
                  className="bg-green-500 text-white px-3 py-1 text-sm rounded hover:bg-green-600"
                  onClick={() => setIsFileModalVisible(true)}
                >
                  <img src={AddFile} alt="Add File" className="w-4 h-4" />
                </button>
              </div> */}
            </div>
          )}
        </div>
      )}
      {node.type === "file" && (
        <div className="flex items-center space-x-2 text-gray-700 cursor-pointer folder-name">
          <span>📄</span>
          {

            // For workflows with runs access level, clicking name should go to runs listing
            node?.access_level === 'runs' ? (
              <span
                className="w-full overflow-hidden whitespace-nowrap text-ellipsis"
                onClick={() => handleWorkflowRuns(node.id)}
              >
                {node.id}-{node.name}
              </span>
            ) : node?.access_level === 'view' ? (
              // For view-only workflows, disable click
              <span
                className="w-full overflow-hidden whitespace-nowrap text-ellipsis"
                onClick={() => (console.log("can not be access"))}
              >
                {node.id}-{node.name}
              </span>
            ) : (
              // For normal workflows, navigate to workflow page

              <span
                className="w-full overflow-hidden whitespace-nowrap text-ellipsis"
                onClick={() => {
                  if (node?.flowType && node.flowType == 'view') {
                    handleCreateNewView(node.id)
                  } else {
                    handleCreateNewWorkflow(node.id)
                  }
                }}
              >
                {node.id}-{node.name}
              </span>
            )
          }
          {
            !node?.share_type && authState?.user?.role?.alias == 'process_engineer' && <>
              <img title="Share" src={shareIcon} onClick={() => setOpenShareModal(true)} alt="Share" className="w-6 h-6 ms-auto icons" />
            </>
          }

          {/* Show Execute button only if not a runs-shared workflow */}
          {(node?.access_level !== 'runs' && node?.flowType != "view") && (
            <img
              title="Execute"
              src={ExecuteIcon}
              onClick={() => handleExecuteButton(node.id)}
              alt="Execute"
              className="w-4 h-4 ms-auto icons"
            />
          )}

          {/* Always show All Runs button */}
          {node?.flowType != "view" && (
            <img
              title="All Runs"
              src={RunIcon}
              onClick={() => handleWorkflowRuns(node.id)}
              alt="Run"
              className="w-4 h-4 ms-auto icons"
            />
          )}


          {/* Only show Delete if not shared */}
          {!node?.share_type && node?.access_level !== 'runs' && (
            <img
              title="Delete"
              src={DeleteIcon}
              onClick={() => showDeleteConfirmModel(node)}
              alt="Delete"
              className="w-4 h-4 ms-auto icons"
            />
          )}
        </div>
      )}

      {/* Modals for adding folders or files */}
      <FolderModal
        visible={isModalVisible}
        onConfirm={(name) => {
          onAdd(node.id, "folder", name, node?.flowType);
          setIsModalVisible(false);
        }}
        onCancel={() => setIsModalVisible(false)}
      />
      <FileModal
        visible={isFileModalVisible}
        onConfirm={(name) => {
          onAdd(node.id, "file", name, node?.flowType);
          setIsFileModalVisible(false);
        }}
        onCancel={() => setIsFileModalVisible(false)}
      />
      <ConfirmDeleteModal
        type={selectedNode?.type as string}
        visible={showConfirmDeleteModel}
        onConfirm={handleDeleteConfirm}
        onCancel={handleDeleteCancel}
        name={selectedNode?.name as string}
      />
      {openShareModal && (
        <TaskStatusModal visible={openShareModal} onClose={() => setOpenShareModal(false)} workflowData={{ id: node.id, name: node.name }} shareType={'workflow'} />)
      }
    </div>
  );
};

interface folderStructure {
  // selectSystems:any,
  // reloadSelectSystems:boolean
  setViewTabOpen?: (val: boolean) => void;
}

const FolderStructure: React.FC<folderStructure> = ({ setViewTabOpen }: any) => {
  const [structure, setStructure] = useState<Node[]>(initialStructure);
  const [sharedWorkflows, setSharedWorkflows] = useState<Node[]>(initialStructure);
  const [views, setViews] = useState<Node[]>(initialStructure);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isFileModalVisible, setIsFileModalVisible] = useState(false);
  const [isModalVisibleOfView, setIsModalVisibleOfView] = useState(false);
  const [isFileModalVisibleOfView, setIsFileModalVisibleOfView] = useState(false);
  const [activeView, setActiveView] = useState<'workflow' | 'shared' | 'views'>('workflow');

  const selectSystems = useSelector((state: any) => state.systems.systems);
  const handleToggle = (id: number) => {
    const toggleOpenState = (nodes: Node[]): Node[] =>
      nodes.map((node) =>
        node.id === id
          ? { ...node, isOpen: !node.isOpen }
          : node.type === "folder"
            ? { ...node, children: toggleOpenState(node.children || []) }
            : node
      );
    setStructure(toggleOpenState(structure));
  };

  const handleViewToggle = (id: number) => {
    const toggleOpenState = (nodes: Node[]): Node[] =>
      nodes.map((node) =>
        node.id === id
          ? { ...node, isOpen: !node.isOpen }
          : node.type === "folder"
            ? { ...node, children: toggleOpenState(node.children || []) }
            : node
      );
    setViews(toggleOpenState(views))
    // setStructure(toggleOpenState(structure));
  };

  useEffect(() => {
    getAllFolders()
    getShareFolder()
    getAllViews()
  }, [selectSystems])


  const getAllFolders = async () => {
    const systemIdsString = selectSystems.length ? selectSystems[0].systems
      ?.map((system: { systemId: number }) => {
        return system.systemId;
      })
      .join(",") : ''
    const apiUrl = systemIdsString ? `/folder/all-folders?systems_id=${systemIdsString}` : '/folder/all-folders'; // Use conditional URL
    // const apiUrl ='/folder/all-folders';
    const response = await getRequest(apiUrl);
    let folderData = response.data.data
    const formatFolderData = (nodes: any[]): any[] =>
      nodes?.map((node) => ({
        ...node,
        isOpen: false,
        // type: 'folder',
        children: node.children ? formatFolderData(node.children) : [], // Recurse for children
      }));

    const formattedData = formatFolderData(folderData);
    setStructure(formattedData);

  }

  const getAllViews = async () => {
    const systemIdsString = selectSystems.length ? selectSystems[0].systems
      ?.map((system: { systemId: number }) => {
        return system.systemId;
      })
      .join(",") : ''
    const apiUrl = systemIdsString ? `/folder/all-view-folders?systems_id=${systemIdsString}` : '/folder/all-view-folders'; // Use conditional URL
    // const apiUrl ='/folder/all-folders';
    const response = await getRequest(apiUrl);
    let folderData = response.data.data
    const formatFolderData = (nodes: any[]): any[] =>
      nodes?.map((node) => ({
        ...node,
        isOpen: false,
        // type: 'folder',
        children: node.children ? formatFolderData(node.children) : [], // Recurse for children
      }));

    const formattedData = formatFolderData(folderData);
    setViews(formattedData);

  }

  const getShareFolder = async () => {
    try {
      const systemIdsString = selectSystems?.length ? selectSystems[0].systems
        ?.map((system: { systemId: number }) => {
          return system.systemId;
        })
        .join(",") : ''

      const apiUrl = systemIdsString
        ? `/shared-workflow?systems_id=${systemIdsString}`
        : "/shared-workflow";
      let response = await getRequest(apiUrl)
      if (response?.data?.data) {
        let sharedFolder: any = []
        sharedFolder.push(response?.data?.data)
        setSharedWorkflows(sharedFolder[0]?.children)

        // setStructure(prev => [...prev, ...sharedFolder]); 
        //   setStructure(prev => {
        //     const filteredPrev = prev.filter(item => item.id !== 0);
        //     const filteredSharedFolder = sharedFolder.filter((item: { id: number; }) => item.id !== 0);
        //     filteredSharedFolder.push(response?.data?.data)
        //     return [...filteredPrev, ...filteredSharedFolder];
        // });
      }
    } catch (error) {
      console.log('error :', error);

    }
  }

  // const handleAdd = (parentId: number | null, type: "folder" | "file", name: string) => {
  //   const newNode: Node = {
  //     id: Date.now(),
  //     name,
  //     type,
  //     ...(type === "folder" ? { isOpen: false, children: [] } : {}),
  //   };

  //   if (parentId === null) {
  //     // Add to root level
  //     setStructure([...structure, newNode]);
  //   } else {
  //     // Add to specific folder
  //     const addNode = (nodes: Node[]): Node[] =>
  //       nodes.map((node) =>
  //         node.id === parentId && node.type === "folder"
  //           ? { ...node, children: [...(node.children || []), newNode] }
  //           : node.type === "folder"
  //           ? { ...node, children: addNode(node.children || []) }
  //           : node
  //       );
  //     setStructure(addNode(structure));
  //   }
  // };

  const handleAdd = async (parentId: number | null, type: "folder" | "file", name: string, flowType?: string) => {
    console.log('name', name)
    console.log('parentId', parentId)
    console.log('typeeee', type)
    const newNode: Node = {
      id: Date.now(), // Temporary ID for rendering; replace with ID from server if returned.
      name,
      type,
      ...(type === "folder" ? { isOpen: false, children: [] } : {}),
    };

    if (type == 'file') {
      const payload = {
        name,
        folder_id: parentId,
        type,
        systems: selectSystems[0]?.systems
      };

      try {
        let endPoint;
        let response;

        if (flowType && flowType === 'view') {
          endPoint = `/view/create-view`;
          // Create view with the correct payload structure
          const viewPayload = {
            name,
            folder_id: parentId,
            structure: {}, // Empty structure initially
            panels: [] // No panels initially
          };
          response = await postRequest(endPoint, viewPayload);
        } else {
          endPoint = `/workflow/create-workflows`;
          response = await postRequest(endPoint, payload);
        }

        if (response.data.data.id) {
          newNode.id = response.data.data.id;
          newNode.flowType = flowType && flowType === 'view' ? 'view' : 'workflow';
        }
      } catch (error) {
        console.error("Error adding folder/file:", error);
        return;
      }
    }
    else {
      const payload = {
        name,
        parent_id: parentId,
        type,
        systems: selectSystems[0]?.systems,
        flowType: flowType ? flowType : 'workflow'
      };

      try {
        const response = await postRequest('/folder/create', payload);

        if (response.data.data.id) {
          newNode.id = response.data.data.id;
          newNode.flowType = response.data.data.type
        }
      } catch (error) {
        console.error("Error adding folder/file:", error);
        return;
      }
    }

    const sortNodes = (nodes: Node[]): Node[] => {
      let data = nodes
        .filter(item => item.id !== 0) // Remove "Shared Workflow" before sorting
        .sort((a, b) => a.name.localeCompare(b.name)) // Sort by name
        .sort((a, b) => (a.type === "folder" && b.type === "file" ? -1 : a.type === "file" && b.type === "folder" ? 1 : 0)); // Sort by type

      const sharedWorkflow = nodes.find(item => item.id === 0);
      // Append "Shared Workflow" at the bottom
      if (sharedWorkflow) {
        data.push(sharedWorkflow);
      }
      return data
    };

    if (parentId === null) {
      if (flowType && flowType == 'view') {
        setViews((prev) => sortNodes([...prev, newNode]));
      } else {
        setStructure((prev) => sortNodes([...prev, newNode]));
      }

    } else {
      const addNode = (nodes: Node[]): Node[] =>
        nodes.map((node) =>
          node.id === parentId && node.type === "folder"
            ? { ...node, children: sortNodes([...(node.children || []), newNode]) }
            : node.type === "folder"
              ? { ...node, children: addNode(node.children || []) }
              : node
        );
      if (flowType && flowType == 'view') {
        setViews((prev) => addNode(prev));
      } else {
        setStructure((prev) => addNode(prev));
      }
    }
  };


  const handleDelete = (id: number, type: "folder" | "file") => {
    const removeNode = (nodes: Node[]): Node[] =>
      nodes.filter((node) => node.id !== id).map((node) =>
        node.type === "folder" ? { ...node, children: removeNode(node.children || []) } : node
      );
    setStructure(removeNode(structure));
  };

  const handleViewDelete = (id: number, type: "folder" | "file") => {
    const removeNode = (nodes: Node[]): Node[] =>
      nodes.filter((node) => node.id !== id).map((node) =>
        node.type === "folder" ? { ...node, children: removeNode(node.children || []) } : node
      );
    setViews(removeNode(views));
  };

  // const handleDelete = (id: number, type: "folder" | "file") => {
  //   const deleteNode = (nodes: Node[]): Node[] =>
  //     nodes
  //       .filter((node) => node.id !== id)
  //       .map((node) =>
  //         node.type === "folder"
  //           ? { ...node, children: deleteNode(node.children || []) }
  //           : node
  //       );

  //       setStructure((prevStructure) => deleteNode(prevStructure));

  // };
  // useEffect(() => {
  //   console.log('structure1111', structure)
  // }, [structure, setStructure])

  const WorkflowSidebar = () => (
    <>
      <div className="flex items-center gap-1 sticky top-0 p-4 pl-0 z-[1] bg-white">
        <h4 className="font-semibold text-lg">WorkFlow Folders</h4>
        <div className="ms-auto flex space-x-2 icons">
          <button
            className=""
            title="Add Folder"
            onClick={() => setIsModalVisible(true)}
          >
            <img src={AddFolder} alt="Add Folder" className="w-4 h-4" />
          </button>
          <button
            title="Add Workflow"
            onClick={() => setIsFileModalVisible(true)}
          >
            <img src={AddFile} alt="Add Workflow" className="w-4 h-4" />
          </button>
        </div>
      </div>
      <div className="divide-box">
        {structure?.map((node) => (
          <Folder
            key={node.id}
            setViewTabOpen={setViewTabOpen}
            node={node}
            onAdd={handleAdd}
            onToggle={handleToggle}
            onDelete={handleDelete}
          />
        ))}
      </div>
    </>
  )


  const SharedWorkflowSidebar = () => (
    <>
      <div className="flex items-center gap-1 sticky p-4 pl-0 top-0 z-[1] bg-white">
        <h4 className="font-semibold text-lg">Shared WorkFlows</h4>
      </div>
      <div className="divide-box">
        {sharedWorkflows && sharedWorkflows.length > 0 ? (
          sharedWorkflows.map((node) => (
            <Folder
              key={node.id}
              setViewTabOpen={setViewTabOpen}
              node={node}
              onAdd={handleAdd}
              onToggle={handleToggle}
              onDelete={handleDelete}
            />
          ))
        ) : (
          <div className="no-data">No shared workflows available.</div>
        )}
      </div>
    </>
  )

  const WorflowViews = () => (
    <>
      <div className="flex items-center gap-1 sticky top-0 p-4 pl-0 z-[1] bg-white">{ }
        <h4 className="font-semibold text-lg">WorkFlow Views</h4>
        <div className="ms-auto flex space-x-2 icons">
          <button
            className=""
            title="Add Folder"
            onClick={() => setIsModalVisibleOfView(true)}
          >
            <img src={AddFolder} alt="Add Folder" className="w-4 h-4" />
          </button>
          <button
            title="Add Workflow"
            onClick={() => setIsFileModalVisibleOfView(true)}
          >
            <img src={AddFile} alt="Add Workflow" className="w-4 h-4" />
          </button>
        </div>
      </div>
      <div className="divide-box">
        {views?.map((node) => (
          <Folder
            key={node.id}
            node={node}
            onAdd={handleAdd}
            setViewTabOpen={setViewTabOpen}
            onToggle={handleViewToggle}
            onDelete={handleViewDelete}
          />
        ))}
      </div>
    </>
  )


  // const items: CollapseProps['items'] = [
  //   {
  //     key: '1',
  //     label:<WorkflowSidebarHeader />,
  //     children: <WorkflowSidebar />,
  //   },
  //   {
  //     key: '2',
  //     label: <SharedWorkflowSidebarHeader />,
  //     children: <SharedWorkflowSidebar />,
  //   },
  //   {
  //     key: '3',
  //     label: <WorkflowViewsHeader />,
  //     children: < WorflowViews />,
  //   },
  // ];

  const renderActiveComponent = () => {
    switch (activeView) {
      case 'workflow':
        return <WorkflowSidebar />;
      case 'shared':
        return <SharedWorkflowSidebar />;
      case 'views':
        return <WorflowViews />;
      default:
        return null;
    }
  };

  return (
    <>
      <div className="sidebar-collapse">
        {/* Add check if process env is steel ai instance */}
        {(process.env.REACT_APP_STEEL_AI_INSTANCE) ? (
          <div></div>
        ) : (
          <Flex className="data-btn-group">
            <Button className={`${activeView === 'workflow' ? 'active' : ''}`}
              onClick={() => setActiveView('workflow')}>WorkFlow Folders</Button>
            <Button className={activeView === 'shared' ? 'active' : ''}
              onClick={() => setActiveView('shared')}>Shared WorkFlows</Button>
            <Button className={activeView === 'views' ? 'active' : ''}
              onClick={() => setActiveView('views')}>WorkFlow Views</Button>
          </Flex>
        )}
        {renderActiveComponent()}
        {/* <Collapse accordion defaultActiveKey="1" items={items} /> */}
      </div>
      {/* Modals for adding root-level folders or files */}
      <FolderModal
        visible={isModalVisible}
        onConfirm={(name) => {
          handleAdd(null, "folder", name);
          setIsModalVisible(false);
        }}
        onCancel={() => setIsModalVisible(false)}
      />
      <FileModal
        visible={isFileModalVisible}
        onConfirm={(name) => {
          handleAdd(null, "file", name);
          setIsFileModalVisible(false);
        }}
        onCancel={() => setIsFileModalVisible(false)}
      />
      <FolderModal
        visible={isModalVisibleOfView}
        onConfirm={(name) => {
          handleAdd(null, "folder", name, 'view');
          setIsModalVisibleOfView(false);
        }}
        onCancel={() => setIsModalVisibleOfView(false)}
      />
      <FileModal
        visible={isFileModalVisibleOfView}
        onConfirm={(name) => {
          handleAdd(null, "file", name, 'view');
          setIsFileModalVisibleOfView(false);
        }}
        onCancel={() => setIsFileModalVisibleOfView(false)}
      />
    </>

  );
};

export default FolderStructure;
