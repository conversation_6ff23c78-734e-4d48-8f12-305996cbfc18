// Define filter types for panels
import { RowSelection, ColumnSelection, DateFilter } from './types';

// Base filter interface that all panel filters will extend
export interface BasePanelFilter {
  id: string;
  type: string;
  enabled: boolean;
}

// Column filter for selecting specific columns
export interface ColumnFilter extends BasePanelFilter {
  type: 'column';
  selection: ColumnSelection;
}

// Row filter for selecting specific rows
export interface RowFilter extends BasePanelFilter {
  type: 'row';
  selection: RowSelection;
}

// Date range filter
export interface DateRangeFilter extends BasePanelFilter {
  type: 'date';
  startDate: string | null;
  endDate: string | null;
}

// Value range filter for numeric columns
export interface ValueRangeFilter extends BasePanelFilter {
  type: 'value-range';
  column: string;
  min: number;
  max: number;
}

// Union type of all filter types
export type PanelFilter = ColumnFilter | RowFilter | DateRangeFilter | ValueRangeFilter;

// Filter state for a panel
export interface PanelFilterState {
  panelId: string;
  filters: PanelFilter[];
}

// Helper functions for working with filters

// Create a column filter
export function createColumnFilter(id: string, selection: ColumnSelection, enabled = true): ColumnFilter {
  return {
    id,
    type: 'column',
    enabled,
    selection
  };
}

// Create a row filter
export function createRowFilter(id: string, selection: RowSelection, enabled = true): RowFilter {
  return {
    id,
    type: 'row',
    enabled,
    selection
  };
}

// Create a date filter
export function createDateFilter(id: string, startDate: string | null, endDate: string | null, enabled = true): DateRangeFilter {
  return {
    id,
    type: 'date',
    enabled,
    startDate,
    endDate
  };
}

// Create a value range filter
export function createValueRangeFilter(id: string, column: string, min: number, max: number, enabled = true): ValueRangeFilter {
  return {
    id,
    type: 'value-range',
    enabled,
    column,
    min,
    max
  };
}

// Get a filter by type from a panel's filter state
export function getFilterByType<T extends PanelFilter>(filters: PanelFilter[], type: string): T | undefined {
  return filters.find(filter => filter.type === type) as T | undefined;
}

// Update a filter in a panel's filter state
export function updateFilter(filters: PanelFilter[], updatedFilter: any): PanelFilter[] {
  const index = filters.findIndex(filter => filter.id === updatedFilter.id);
  if (index === -1) {
    return [...filters, updatedFilter];
  }

  const newFilters = [...filters];
  newFilters[index] = updatedFilter;
  return newFilters;
}

// Convert legacy filter props to the new filter system
export function convertLegacyFilters(
  selectedRows: RowSelection,
  selectedColumns: ColumnSelection,
  dateFilter: DateFilter
): PanelFilter[] {
  const filters: PanelFilter[] = [];

  // Add row filter if there are selected rows
  if (selectedRows.indices.length > 0) {
    filters.push(createRowFilter('row-filter', selectedRows));
  }

  // Add column filter if there are selected columns
  if (selectedColumns.indices.length > 0) {
    filters.push(createColumnFilter('column-filter', selectedColumns));
  }

  // Add date filter if there is a date range
  if (dateFilter.startDate || dateFilter.endDate) {
    filters.push(createDateFilter('date-filter', dateFilter.startDate, dateFilter.endDate));
  }

  return filters;
}
