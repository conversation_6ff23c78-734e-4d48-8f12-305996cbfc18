import React from 'react';
import { Select, But<PERSON>, Space, Divider, Spin } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { ComponentType, FileData } from './types';
import { useNavigate } from 'react-router-dom';

interface ViewSidebarProps {
  files?: FileData[];
  selectedFile?: FileData | null;
  onFileSelect?: (file: FileData) => void;
  onBackClick?: () => void;
  activePanels?: ComponentType[];
  isLoading?: boolean;
}

const ViewSidebar: React.FC<ViewSidebarProps> = ({
  files = [],
  selectedFile = null,
  onFileSelect,
  onBackClick,
  activePanels = [],
  isLoading = false
}) => {
  const navigate = useNavigate();


  const handleBackClick = () => {
    if (onBackClick) {
      onBackClick();
    } else {
      // Default behavior: navigate back to workflow folders
      navigate('/?tab=insight&workflowId=0');
    }
  };

  const handleFileChange = (fileId: string) => {
    const file = files.find(f => f.csv_id === fileId);
    if (file && onFileSelect) {
      onFileSelect(file);
    }
  };

  return (
    <div className="view-sidebar p-4 shadow-md h-full flex flex-col">
      <div className="sidebar-header mb-4">
        <Space direction="vertical" style={{ width: '100%' }}>
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            onClick={handleBackClick}
            style={{ padding: 0 }}
          >
            Back to Workflows
          </Button>

          {isLoading ? (
            <div style={{ textAlign: 'center', padding: '10px 0' }}>
              <Spin size="small" /> Loading files...
            </div>
          ) : (
            <Select
              placeholder="Select a file"
              style={{ width: '100%' }}
              value={selectedFile?.csv_id || undefined}
              onChange={handleFileChange}
              options={files.map(file => ({
                value: file.csv_id,
                label: file.file_name
              }))}
            />
          )}
        </Space>
      </div>

      <Divider style={{ margin: '12px 0' }} />

      {selectedFile && (
        <div className="sidebar-components flex-grow">
          <h3 className="text-base font-medium mb-3">Available Panels</h3>
          <ul className="component-list">
            <li
              className={`component-item p-2 mb-2 rounded cursor-pointer ${activePanels.includes(ComponentType.TimeSeriesPanel) ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100'}`}
              draggable={!activePanels.includes(ComponentType.TimeSeriesPanel)}
              onDragStart={(e) => {
                if (activePanels.includes(ComponentType.TimeSeriesPanel)) return;
                e.dataTransfer.setData('component', ComponentType.TimeSeriesPanel);
                console.log('Drag started: TimeSeriesPanel');
                e.dataTransfer.effectAllowed = 'move';
              }}
            >
              Time Series {activePanels.includes(ComponentType.TimeSeriesPanel) && <span className="added-badge ml-2 text-xs bg-gray-200 px-1 rounded">Added</span>}
            </li>
            <li
              className={`component-item p-2 mb-2 rounded cursor-pointer ${activePanels.includes(ComponentType.OverviewPanel) ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100'}`}
              draggable={!activePanels.includes(ComponentType.OverviewPanel)}
              onDragStart={(e) => {
                if (activePanels.includes(ComponentType.OverviewPanel)) return;
                e.dataTransfer.setData('component', ComponentType.OverviewPanel);
                console.log('Drag started: OverviewPanel');
                e.dataTransfer.effectAllowed = 'move';
              }}
            >
              Overview {activePanels.includes(ComponentType.OverviewPanel) && <span className="added-badge ml-2 text-xs bg-gray-200 px-1 rounded">Added</span>}
            </li>
            <li
              className={`component-item p-2 mb-2 rounded cursor-pointer ${activePanels.includes(ComponentType.HistogramPanel) ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100'}`}
              draggable={!activePanels.includes(ComponentType.HistogramPanel)}
              onDragStart={(e) => {
                if (activePanels.includes(ComponentType.HistogramPanel)) return;
                e.dataTransfer.setData('component', ComponentType.HistogramPanel);
                console.log('Drag started: HistogramPanel');
                e.dataTransfer.effectAllowed = 'move';
              }}
            >
              Histogram {activePanels.includes(ComponentType.HistogramPanel) && <span className="added-badge ml-2 text-xs bg-gray-200 px-1 rounded">Added</span>}
            </li>
            <li
              className={`component-item p-2 mb-2 rounded cursor-pointer ${activePanels.includes(ComponentType.DataTablePanel) ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100'}`}
              draggable={!activePanels.includes(ComponentType.DataTablePanel)}
              onDragStart={(e) => {
                if (activePanels.includes(ComponentType.DataTablePanel)) return;
                e.dataTransfer.setData('component', ComponentType.DataTablePanel);
                console.log('Drag started: DataTablePanel');
                e.dataTransfer.effectAllowed = 'move';
              }}
            >
              Data Table {activePanels.includes(ComponentType.DataTablePanel) && <span className="added-badge ml-2 text-xs bg-gray-200 px-1 rounded">Added</span>}
            </li>
          </ul>
        </div>
      )}

      {!selectedFile && (
        <div className="flex-grow flex items-center justify-center text-gray-500">
          <p>Select a file to view available panels</p>
        </div>
      )}
    </div>
  );
};

export default ViewSidebar;
