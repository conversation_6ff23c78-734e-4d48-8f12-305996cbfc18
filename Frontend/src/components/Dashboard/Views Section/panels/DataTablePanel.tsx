import React, { useEffect, useRef } from 'react';
import { RowSelection, ColumnSelection, DateFilter } from '../types';
import { PanelFilter } from '../FilterTypes';
import { Spin } from 'antd';
import Handsontable from 'handsontable';
import 'handsontable/dist/handsontable.full.css';
import '../DateFilterPanel';
import DateFilterPanel from '../DateFilterPanel';

interface DataTablePanelProps {
  data: any;
  selectedRows?: RowSelection;
  selectedColumns?: ColumnSelection;
  dateFilter?: DateFilter;
  isLoading?: boolean;
  panelFilters?: PanelFilter[];
  onRowSelection?: (indices: number[], data: any[]) => void;
  onColumnSelection?: (indices: number[], headers: string[]) => void;
  onDateFilterChange?: (startDate: string | null, endDate: string | null) => void;
}

const DataTablePanel: React.FC<DataTablePanelProps> = ({
  data,
  selectedRows = { indices: [], data: [] },
  selectedColumns = { indices: [], headers: [] },
  dateFilter = { startDate: null, endDate: null },
  isLoading = false,
  panelFilters = [],
  // These props are kept for type compatibility but not used
  onRowSelection,
  onColumnSelection,
  onDateFilterChange
}) => {
  const tableRef = useRef<HTMLDivElement>(null);
  const hotRef = useRef<Handsontable | null>(null);

  // No need for internal selection state since we're disabling clicking

  useEffect(() => {
    console.log('DataTablePanel - useEffect triggered');
    console.log('DataTablePanel - panelFilters:', panelFilters);

    if (tableRef.current && data && !isLoading) {
      if (hotRef.current) {
        hotRef.current.destroy();
      }

      // Extract headers and data
      let headers: string[] = [];
      let tableData: any[][] = [];

      if (Array.isArray(data) && data.length > 0) {
        // Assuming first row contains headers
        headers = Array.isArray(data[0]) ? data[0].map(String) : Object.keys(data[0]);
        tableData = Array.isArray(data[0])
          ? data.slice(1)
          : data.map((row: any) => Object.values(row));
      }

      // Apply date filter if provided
      let filteredData = tableData;
      console.log('DataTablePanel - Initial data rows:', filteredData.length);

      if (dateFilter.startDate && dateFilter.endDate) {
        // Find date column (usually the first column)
        const dateColumnIndex = headers.findIndex(header =>
          header.toLowerCase().includes('date') ||
          header.toLowerCase().includes('time')
        );

        const dateIndex = dateColumnIndex >= 0 ? dateColumnIndex : 0;
        console.log(`DataTablePanel - Using date column: ${headers[dateIndex]} (index: ${dateIndex})`);

        try {
          // Parse dates with time for comparison
          const startDate = new Date(dateFilter.startDate as string);
          const endDate = new Date(dateFilter.endDate as string);

          // Ensure valid dates
          if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
            console.log(`Filtering by date/time: ${startDate.toISOString()} to ${endDate.toISOString()}`);

            filteredData = tableData.filter(row => {
              if (!row[dateIndex]) return false;

              try {
                const dateStr = row[dateIndex].toString();
                const rowDate = new Date(dateStr);

                if (isNaN(rowDate.getTime())) return false;

                // Compare with full date and time
                return rowDate >= startDate && rowDate <= endDate;
              } catch (e) {
                return false;
              }
            });

            console.log(`DataTablePanel - After date filtering: ${filteredData.length} rows remaining`);
          } else {
            console.error(`Invalid date range: ${dateFilter.startDate} - ${dateFilter.endDate}`);
          }
        } catch (e) {
          console.error('Error parsing date filter:', e);
        }
      }

      // Apply value range filters from zoom selection
      if (panelFilters && panelFilters.length > 0) {
        console.log('DataTablePanel - Applying panel filters:', panelFilters);

        // Find value range filters
        const valueRangeFilters = panelFilters.filter(f => f.type === 'value-range' && f.enabled);
        console.log('DataTablePanel - Value range filters:', valueRangeFilters);

        if (valueRangeFilters.length > 0) {
          // Apply each value range filter
          valueRangeFilters.forEach(filter => {
            const valueFilter = filter as any; // Type assertion to access column, min, max
            console.log(`DataTablePanel - Applying filter for column: ${valueFilter.column}, min: ${valueFilter.min}, max: ${valueFilter.max}`);

            // Find the column index
            const columnIndex = headers.findIndex(h => h === valueFilter.column);
            console.log(`DataTablePanel - Column index for ${valueFilter.column}: ${columnIndex}`);

            if (columnIndex >= 0) {
              // Filter the data based on the value range
              filteredData = filteredData.filter(row => {
                const value = parseFloat(row[columnIndex]);
                return !isNaN(value) && value >= valueFilter.min && value <= valueFilter.max;
              });
              console.log(`DataTablePanel - After filtering by ${valueFilter.column}: ${filteredData.length} rows`);
            }
          });
        }
      }

      // Apply column selection if provided
      let visibleColumns = headers;
      let visibleData = filteredData;

      if (selectedColumns.indices.length > 0) {
        // Get the date column (usually first column)
        const dateColumnIndex = 0;

        // Create a new array of selected column indices that always includes the date column
        const selectedIndices = selectedColumns.indices.includes(dateColumnIndex)
          ? selectedColumns.indices
          : [dateColumnIndex, ...selectedColumns.indices];

        // Sort indices to maintain original column order
        selectedIndices.sort((a, b) => a - b);

        // Filter headers to only include selected columns
        visibleColumns = selectedIndices.map(idx => headers[idx]);

        // Filter data to only include selected columns
        visibleData = filteredData.map(row =>
          selectedIndices.map(colIdx => row[colIdx])
        );
      }


      const dataWithCheckboxes = visibleData;

      const headersWithCheckbox = visibleColumns;

      hotRef.current = new Handsontable(tableRef.current, {
        data: dataWithCheckboxes,
        colHeaders: headersWithCheckbox,
        rowHeaders: true,
        height: '100%',
        width: '100%',
        licenseKey: 'non-commercial-and-evaluation',
        stretchH: 'all',
        contextMenu: false, // Disable right-click menu
        manualColumnResize: false, // Disable column resizing
        manualRowResize: false, // Disable row resizing
        manualColumnMove: false, // Disable column moving
        manualRowMove: false, // Disable row moving
        className: 'htDark',
        disableVisualSelection: true, // Disable visual selection
        fragmentSelection: false, // Disable fragment selection
        columns: [
          ...Array(headersWithCheckbox.length).fill({ type: 'text', readOnly: true }) // Make data columns read-only
        ],


        // Disable selection handling
        afterSelectionEnd: () => {
          // Do nothing - selection is disabled
        },
        // Only highlight rows/columns from external selections (not from clicking)
        cells: function(row, col) {
          const cellProperties: { className?: string } = {};

          // Only apply highlighting from parent component selections
          if (selectedRows.indices.includes(row)) {
            cellProperties.className = 'selected-row';
          }

          // Only highlight columns selected from Overview panel
          if (selectedColumns.indices.includes(col)) {
            cellProperties.className = (cellProperties.className || '') + ' selected-column';
          }

          return cellProperties;
        }
      });
    }

    return () => {
      if (hotRef.current) {
        hotRef.current.destroy();
        hotRef.current = null;
      }
    };
  }, [data, dateFilter, isLoading, selectedColumns, panelFilters]);

  // Handle date filter changes
  const handleDateFilterChange = (startDate: string | null, endDate: string | null) => {
    if (onDateFilterChange) {
      onDateFilterChange(startDate, endDate);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-full">
        <Spin size="large" tip="Loading data..." />
      </div>
    );
  }

  if (!data) {
    return (
      <div className="flex justify-center items-center h-full">
        <p>No data available for table</p>
      </div>
    );
  }

  return (
    <div className="data-table-panel h-full flex flex-col">
      <div className="data-table-header p-2">
        <DateFilterPanel
          onDateFilterChange={handleDateFilterChange}
          dateFilter={dateFilter}
        />
      </div>
      <div
        ref={tableRef}
        className="flex-grow overflow-auto"
        style={{ height: onDateFilterChange ? 'calc(100% - 50px)' : '100%' }}
      />
    </div>
  );
};

export default DataTablePanel;
