import React, { useEffect, useMemo, useRef, useState } from 'react';
import { RowSelection, ColumnSelection, DateFilter } from '../types';
import { PanelFilter } from '../FilterTypes';
import { Spin, Empty, Tabs } from 'antd';
import Plot from 'react-plotly.js';

interface TimeSeriesPanelProps {
  data: any;
  selectedRows?: RowSelection;
  selectedColumns?: ColumnSelection;
  dateFilter?: DateFilter;
  isLoading?: boolean;
  panelFilters?: PanelFilter[];
  onZoomSelection?: (column: string, min: number, max: number, sourcePanelId?: string) => void;
}

const TimeSeriesPanel: React.FC<TimeSeriesPanelProps> = ({
  data,
  selectedRows = { indices: [], data: [] },
  selectedColumns = { indices: [], headers: [] },
  dateFilter = { startDate: null, endDate: null },
  isLoading = false,
  panelFilters = [],
  onZoomSelection
}) => {
  
  const containerRef = useRef<HTMLDivElement>(null);
  const [plotHeight, setPlotHeight] = useState(300);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const resizeObserver = new ResizeObserver(entries => {
      for (let entry of entries) {
        const height = entry.contentRect.height;
        setPlotHeight(Math.floor(height * 0.8));
      }
    });

    resizeObserver.observe(container);

    return () => resizeObserver.disconnect();
  }, []);
  
  
  // Process data for the time series chart
  const plotData = useMemo(() => {
    console.log('TimeSeriesPanel - useMemo triggered');
    console.log('TimeSeriesPanel - panelFilters:', panelFilters);
    if (!data || isLoading) return [];

    try {
      // Check if we have time series data
      if (!data.timeSeries || !Array.isArray(data.timeSeries)) {
        // Try to extract time series data from tabular data
        if (Array.isArray(data) && data.length > 0) {
          // Assuming first column is date/time and other columns are values
          const headers = Array.isArray(data[0]) ? data[0] : Object.keys(data[0]);
          const rows = Array.isArray(data[0]) ? data.slice(1) : data;

          // Find date/time column (usually the first column)
          const dateColumnIndex = headers.findIndex(h =>
            h.toLowerCase().includes('time') ||
            h.toLowerCase().includes('date')
          );

          if (dateColumnIndex >= 0) {
            // Create a series for each numeric column
            const series = [];

            for (let i = 0; i < headers.length; i++) {
              if (i !== dateColumnIndex) {
                const columnData = rows.map(row => {
                  const value = Array.isArray(row) ? row[i] : row[headers[i]];
                  return parseFloat(value);
                }).filter(val => !isNaN(val));

                if (columnData.length > 0) {
                  series.push({
                    name: headers[i],
                    data: columnData,
                    x: rows.map(row => Array.isArray(row) ? row[dateColumnIndex] : row[headers[dateColumnIndex]]),
                    y: columnData,
                    type: 'scatter',
                    mode: 'lines+markers',
                  });
                }
              }
            }

            return series;
          }
        }
        return [];
      }

      // If we have structured time series data
      if (data.timeSeries.categories && data.timeSeries.series) {
        return data.timeSeries.series.map((series: any) => ({
          name: series.name,
          x: data.timeSeries.categories,
          y: series.data,
          type: 'scatter',
          mode: 'lines+markers',
        }));
      }

      return [];
    } catch (error) {
      console.error('Error processing time series data:', error);
      return [];
    }
  }, [data, isLoading]);

  // Apply filters
  const filteredData = useMemo(() => {
    let filtered = [...plotData];

    // Apply date filter
    if (dateFilter.startDate && dateFilter.endDate) {
      const startDate = new Date(dateFilter.startDate);
      const endDate = new Date(dateFilter.endDate);

      filtered = filtered.map(series => {
        const filteredPoints = series.x.map((date: string, index: number) => {
          const pointDate = new Date(date);
          return { date: pointDate, index };
        }).filter((point: any) => {
          return point.date >= startDate && point.date <= endDate;
        });

        return {
          ...series,
          x: filteredPoints.map((point: any) => series.x[point.index]),
          y: filteredPoints.map((point: any) => series.y[point.index]),
        };
      });
    }

    // Apply row selection if any
    if (selectedRows.indices.length > 0 && selectedRows.data.length > 0) {
      // For row selection, we'll create a stacked graph with the selected rows
      // Extract the time column (assuming it's the first column)
      const timeColumn = selectedRows.data.map(row => row[0]);

      // Create a series for each selected row
      return selectedRows.data.map((row, index) => {
        // Find numeric values in the row (skip the first column which is time)
        const values = row.slice(1).map((val: any) => parseFloat(val)).filter((val: any) => !isNaN(val));

        return {
          name: `Selection ${index + 1}`,
          x: timeColumn,
          y: values,
          type: 'scatter',
          mode: 'lines+markers',
        };
      });
    }

    // Apply column selection if any
    if (selectedColumns.indices.length > 0 && selectedColumns.headers.length > 0) {
      // Filter series by selected column headers
      filtered = filtered.filter(series =>
        selectedColumns.headers.includes(series.name)
      );
    }

    return filtered;
  }, [plotData, selectedRows, selectedColumns, dateFilter]);

  // We'll remove the toggle functionality as requested

  // Configure layout for tabbed view
  const layout = {
    title: 'Time Series',
    autosize: true,
    height:plotHeight,
    margin: { l: 50, r: 50, b: 50, t: 50, pad: 4 },
    xaxis: {
      title: 'Time',
      showgrid: true,
      zeroline: false,
    },
    yaxis: {
      title: 'Value',
      showgrid: true,
      zeroline: false,
    },
    showlegend: true,
    legend: {
      x: 0,
      y: 1,
      orientation: 'h'
    },
    hovermode: 'closest',
    paper_bgcolor: 'rgba(0,0,0,0)',
    plot_bgcolor: 'rgba(0,0,0,0)',
  };

  // Create stacked chart layout for selected columns
  const createStackedChartLayout = (selectedSeries: any[]) => {
    // Handle case with no series
    if (!selectedSeries || selectedSeries.length === 0) {
      // Return a copy of the outer layout to avoid the block-scoped variable error
      return { ...layout };
    }

    // Create a new layout object for stacked charts
    const stackedLayout: any = {
      grid: {
        rows: selectedSeries.length,
        columns: 1,
        pattern: 'independent',
        roworder: 'top to bottom',
        // Add significant spacing between subplots to ensure they're stacked, not superimposed
        rowgap: 0.15
      },
      title: 'Time Series for Selected Columns',
      showlegend: true,
      // Enable box selection
      dragmode: 'zoom',
      selectdirection: 'h', // Horizontal selection only
      legend: {
        x: 0,
        y: 1,
        orientation: 'h'
      },
      // Increase left margin for y-axis labels
      margin: { l: 80, r: 50, b: 50, t: 70, pad: 4 },
      paper_bgcolor: 'rgba(0,0,0,0)',
      plot_bgcolor: 'rgba(0,0,0,0)',
      hovermode: 'closest',
      // Add height to accommodate all subplots - increase height per subplot
      height: Math.max(600, selectedSeries.length * 250),
    };

    // Create subplots array
    const subplots: string[][] = [];

    // Configure each y-axis and add to subplots
    selectedSeries.forEach((series, index) => {
      const yAxisId = index + 1;

      // Add to subplots array
      subplots.push([`xy${yAxisId}`]);

      // Calculate domain for proper stacking - divide the vertical space
      // Each subplot gets its own section of the vertical space
      const domainSize = 1.0 / selectedSeries.length;
      const domainStart = 1.0 - (index + 1) * domainSize;
      const domainEnd = 1.0 - index * domainSize;

      // Configure y-axis
      stackedLayout[`yaxis${yAxisId}`] = {
        title: {
          text: series.name,
          font: {
            size: 12,
            color: '#7f7f7f'
          }
        },
        showgrid: true,
        zeroline: false,
        // Set specific domain to ensure proper stacking
        domain: [domainStart, domainEnd - 0.05], // 0.05 gap between plots
        // Auto-scale each subplot
        autorange: true,
        // Add gridlines for better readability
        gridcolor: 'rgba(200, 200, 200, 0.2)',
      };
    });

    // Configure shared x-axis
    stackedLayout.xaxis = {
      title: {
        text: 'Time',
        font: {
          size: 14,
          color: '#7f7f7f'
        }
      },
      showgrid: true,
      zeroline: false,
      // Add gridlines for better readability
      gridcolor: 'rgba(200, 200, 200, 0.2)',
      // Auto-scale the x-axis
      autorange: true,
      // Format dates nicely
      type: 'date',
      tickformat: '%Y-%m-%d %H:%M:%S',
    };

    // Set subplots in grid
    stackedLayout.grid.subplots = subplots;

    return stackedLayout;
  };

  // Modify the plot data for stacked view
  const createStackedChartData = (selectedSeries: any[]) => {
    // Handle case with no series
    if (!selectedSeries || selectedSeries.length === 0) {
      return [];
    }

    // Assign different colors to each series
    const colors = [
      '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
      '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'
    ];

    return selectedSeries.map((series, index) => ({
      ...series,
      yaxis: `y${index + 1}`,
      name: series.name,
      line: {
        color: colors[index % colors.length],
        width: 2
      },
      marker: {
        color: colors[index % colors.length],
        size: 6
      }
    }));
  };

  // Configure plot options
  const config = {
    responsive: true,
    displayModeBar: false,
    doubleClick: 'reset',
    displaylogo: false
  };

  // Handle zoom selection events
  const handleZoomSelection = (eventData: any) => {
    if (!onZoomSelection || !eventData) return;

    // Extract the selected range from the event data
    let xRange;
    if (eventData['xaxis.range[0]'] && eventData['xaxis.range[1]']) {
      xRange = [eventData['xaxis.range[0]'], eventData['xaxis.range[1]']];
    } else if (eventData['xaxis.range']) {
      xRange = eventData['xaxis.range'];
    }

    if (!xRange || xRange.length !== 2) return;

    // For time series, always filter by date
    const dateColumn = 'date';
    const min = xRange[0];
    const max = xRange[1];

    // Call the parent's onZoomSelection handler
    onZoomSelection(dateColumn, min, max, 'time-series-panel');
  };

  // Group data by column for tabs - moved before early returns
  const columnTabs = useMemo(() => {
    // If no filtered data, return empty array
    if (!filteredData || filteredData.length === 0) {
      return [];
    }

    // If we have row selections, show them in a single tab
    if (selectedRows.indices.length > 0 && selectedRows.data.length > 0) {
      return [{
        name: 'Selected Rows',
        data: filteredData
      }];
    }

    // Otherwise, create a tab for each column
    return filteredData.map(series => ({
      name: series.name,
      data: [series]
    }));
  }, [filteredData, selectedRows]);

  // Early returns after all hooks are called
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-full">
        <Spin size="large" tip="Loading time series data..." />
      </div>
    );
  }

  if (!filteredData || filteredData.length === 0) {
    return (
      <div className="flex justify-center items-center h-full">
        <Empty description="No time series data available" />
      </div>
    );
  }

  // Determine if we should show stacked view - always show stacked view when columns are selected
  const shouldShowStackedView = selectedColumns.indices.length > 0 && filteredData.length > 0;

  // if (shouldShowStackedView) {
    // Stacked chart view for selected columns
    const stackedLayout = createStackedChartLayout(filteredData);
    const stackedData = createStackedChartData(filteredData);

    return (
      <div className="time-series-panel h-full" ref={containerRef}>
        {
          shouldShowStackedView ? (
          <>
          <div className="flex justify-between items-center mb-2 px-4 pt-2">
          <h3 className="text-lg font-medium">Time Series for Selected Columns ({filteredData.length})</h3>
        </div>
        <div className="p-4 h-[calc(100%-40px)] overflow-auto">
          <div style={{ minHeight: `${Math.max(400, filteredData.length * 150)}px` }}>
            <Plot
              data={stackedData as any}
              layout={stackedLayout as any}
              config={config as any}
              onRelayout={handleZoomSelection}
              style={{ width: '100%', height: '100%' }}
              useResizeHandler={true}
            />
          </div>
        </div></>) :(<>
          <Tabs defaultActiveKey="0" style={{ height: '100%' }}>
          {columnTabs.map((tab, index) => (
            <Tabs.TabPane tab={tab.name} key={index.toString()}>
              <div className="p-4 h-full">
                <Plot
                  data={tab.data as any}
                  layout={{
                    ...layout,
                    title: `Time Series: ${tab.name}`,
                    showlegend: tab.data.length > 1
                  } as any}
                  config={config as any}
                  style={{ width: '100%', height: 'calc(100% - 20px)' }}
                  useResizeHandler={true}
                  onRelayout={handleZoomSelection}
                />
              </div>
            </Tabs.TabPane>
          ))}
        </Tabs></>)
        }
      </div>
    );
  // } else {
  //   // Original tab-based view
  //   return (
  //     <div className="time-series-panel h-full">
  //       <Tabs defaultActiveKey="0" style={{ height: '100%' }}>
  //         {columnTabs.map((tab, index) => (
  //           <Tabs.TabPane tab={tab.name} key={index.toString()}>
  //             <div className="p-4 h-full">
  //               <Plot
  //                 data={tab.data as any}
  //                 layout={{
  //                   ...layout,
  //                   title: `Time Series: ${tab.name}`,
  //                   showlegend: tab.data.length > 1
  //                 } as any}
  //                 config={config as any}
  //                 style={{ width: '100%', height: 'calc(100% - 20px)' }}
  //                 useResizeHandler={true}
  //               />
  //             </div>
  //           </Tabs.TabPane>
  //         ))}
  //       </Tabs>
  //     </div>
  //   );
  // }
};

export default TimeSeriesPanel;
