import React, { useRef } from 'react';
import { FileData, ComponentType, RowSelection, ColumnSelection, DateFilter } from './types';
import { PanelFilter } from './FilterTypes';
import GridLayout from './GridLayout';
import { Button, message } from 'antd';
import { SaveOutlined, ClearOutlined } from '@ant-design/icons';
import { putRequest } from '../../../utils/apiHandler';
import Notiflix from 'notiflix';
import TabsInterface from './Tabs/TabsInterface';

interface ViewContentProps {
  selectedFile: FileData | null;
  createInitialPanels?: boolean;
  activePanels?: ComponentType[];
  onPanelsChange?: (activePanels: ComponentType[]) => void;

  // Selection and filter props
  selectedRows?: RowSelection;
  selectedColumns?: ColumnSelection;
  dateFilter?: DateFilter;
  panelFilters?: Record<string, PanelFilter[]>;

  // Selection and filter handlers
  onRowSelection?: (indices: number[], data: any[]) => void;
  onColumnSelection?: (indices: number[], headers: string[]) => void;
  onDateFilterChange?: (startDate: string | null, endDate: string | null) => void;
  onZoomSelection?: (column: string, min: number, max: number, sourcePanelId?: string) => void;

  // View structure from database
  structure?: any;
}

const ViewContent: React.FC<ViewContentProps> = ({
  selectedFile,
  createInitialPanels = false,
  activePanels = [],
  onPanelsChange,
  selectedRows = { indices: [], data: [] },
  selectedColumns = { indices: [], headers: [] },
  dateFilter = { startDate: null, endDate: null },
  panelFilters = {},
  onRowSelection,
  onColumnSelection,
  onDateFilterChange,
  onZoomSelection,
  structure
}) => {
  // Reference to the GridLayout component
  const gridLayoutRef = useRef<{getLayout: () => any[], getItems: () => any[]}>(null);

  // Helper function to map component types to panel type IDs
  const getPanelTypeId = (componentType: ComponentType): number => {
    switch (componentType) {
      case ComponentType.TimeSeriesPanel:
        return 1;
      case ComponentType.OverviewPanel:
        return 2;
      case ComponentType.HistogramPanel:
        return 3;
      case ComponentType.DataTablePanel:
        return 4;
      default:
        return 0;
    }
  };

  // Function to handle saving the view
  const handleSaveView = async () => {
    if (!selectedFile || !gridLayoutRef.current) {
      message.error('Please select a file first');
      return;
    }

    try {
      Notiflix.Loading.circle('Saving view...');

      // Get the current layout and items from the GridLayout component
      const currentLayout = gridLayoutRef.current.getLayout();
      const currentItems = gridLayoutRef.current.getItems();

      if (!currentLayout || !currentItems || currentLayout.length === 0) {
        message.error('No layout or items found');
        Notiflix.Loading.remove();
        return;
      }

      // Get the view ID from the URL if it exists
      const searchParams = new URLSearchParams(window.location.search);
      const viewId = searchParams.get('viewId');

      // Create a combined layout with panel types
      const layout = currentLayout.map((layoutItem: any) => {
        // Find the corresponding item to get its type
        const item = currentItems.find((i: any) => i.id === layoutItem.i);
        return {
          ...layoutItem,
          panelType: item ? item.type : ComponentType.TimeSeriesPanel
        };
      });

      // Create panel data for each panel
      const panels = currentItems.map((item: any) => {
        // Find the corresponding layout item
        const layoutItem = currentLayout.find((l: any) => l.i === item.id) || { x: 0, y: 0, w: 6, h: 6 };

        // Get panel-specific filters
        let panelSpecificFilters = {};
        if (panelFilters && panelFilters[item.id]) {
          panelSpecificFilters = { filters: panelFilters[item.id] };
        }

        // For overview panel, save the checkbox state
        if (item.type === 'overview' && selectedColumns && selectedColumns.indices.length > 0) {
          panelSpecificFilters = {
            ...panelSpecificFilters,
            selectedColumns: selectedColumns
          };
        }

        return {
          view_id: viewId || 'new_view',
          panel_type_id: getPanelTypeId(item.type),
          configuration: {
            title: item.title || 'Panel',
            position: {
              x: layoutItem.x,
              y: layoutItem.y,
              w: layoutItem.w,
              h: layoutItem.h
            },
            // Include panel filters that apply to all panels
            panelFilters: {
              selectedRows: selectedRows,
              selectedColumns: selectedColumns,
              dateFilter: dateFilter
            },
            // Include panel-specific filters
            ...panelSpecificFilters
          }
        };
      });

      if (viewId) {
        // If viewId exists, update the existing view
        const updateData = {
          view: {
            structure: layout,
            csvfile_id: selectedFile.csv_id
          },
          panel: panels
        };

        // Update the view structure
        const response = await putRequest(`/view/${viewId}`, updateData);

        if (response.data && response.data.status === 200) {
          message.success('View updated successfully');
          Notiflix.Loading.remove();
        } else {
          message.error('Failed to update view');
          Notiflix.Loading.remove();
        }
      } else {
        // If no viewId, this is an error - views should be created from the folder structure
        message.error('No view ID found. Please create a view from the folder structure first.');
        Notiflix.Loading.remove();
      }
    } catch (error) {
      console.error('Error saving view:', error);
      message.error('Error saving view');
      Notiflix.Loading.remove();
    }
  };



  return (
    <>
    <TabsInterface />
    <div className="view-content flex-1 p-4 h-full relative">
      {selectedFile ? (
        <div className="h-full">
          {/* Floating Save Button */}
          <div className="flex mb-4 gap-2">
            <Button
              type="default"
              icon={<ClearOutlined />}
              onClick={() => {
                // Clear all filters
                if (onRowSelection) onRowSelection([], []);
                if (onColumnSelection) onColumnSelection([], []);
                if (onDateFilterChange) onDateFilterChange(null, null);
              }}
              className="ms-auto"
              style={{ boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)' }}
            >
              Clear Filters
            </Button>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={handleSaveView}
              style={{ boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)' }}
            >
              Save View
            </Button>
          </div>

          <GridLayout
            ref={gridLayoutRef}
            selectedFile={selectedFile}
            createInitialPanels={createInitialPanels}
            onPanelsChange={onPanelsChange}
            selectedRows={selectedRows}
            selectedColumns={selectedColumns}
            dateFilter={dateFilter}
            panelFilters={panelFilters}
            onRowSelection={onRowSelection}
            onColumnSelection={onColumnSelection}
            onDateFilterChange={onDateFilterChange}
            onZoomSelection={onZoomSelection}
            structure={structure}
          />
        </div>
      ) : (
        <div className="flex items-center justify-center h-full">
          <p className="text-gray-500">Select a file from the sidebar to view content</p>
        </div>
      )}
    </div>
    </>
  );
};

export default ViewContent;
