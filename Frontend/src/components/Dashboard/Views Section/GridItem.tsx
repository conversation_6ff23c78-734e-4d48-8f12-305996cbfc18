import React, { useState, useRef } from 'react';
import { ComponentType, GridItemData, RowSelection, ColumnSelection, DateFilter } from './types';
import { PanelFilter } from './FilterTypes';
import TimeSeriesPanel from './panels/TimeSeriesPanel';
import OverviewPanel from './panels/OverviewPanel';
import HistogramPanel from './panels/HistogramPanel';
import DataTablePanel from './panels/DataTablePanel';
import { CloseOutlined } from '@ant-design/icons';
import PanelOptionsMenu from './PanelOptionsMenu';

interface GridItemProps {
  file_id: string;
  data: GridItemData;
  fileData: any;
  onRemove: (id: string) => void;

  // Selection and filter props
  selectedRows?: RowSelection;
  selectedColumns?: ColumnSelection;
  dateFilter?: DateFilter;

  // Panel-specific filters
  panelFilters?: PanelFilter[];

  // Selection and filter handlers
  onRowSelection?: (indices: number[], data: any[]) => void;
  onColumnSelection?: (indices: number[], headers: string[]) => void;
  onDateFilterChange?: (startDate: string | null, endDate: string | null) => void;
  onZoomSelection?: (column: string, min: number, max: number, sourcePanelId?: string) => void;
}

const GridItem: React.FC<GridItemProps> = ({
  data,
  fileData,
  file_id,
  onRemove,
  selectedRows = { indices: [], data: [] },
  selectedColumns = { indices: [], headers: [] },
  dateFilter = { startDate: null, endDate: null },
  panelFilters = [],
  onRowSelection,
  onColumnSelection,
  onDateFilterChange,
  onZoomSelection,
}) => {
  // State for panel display
  const [isFullscreen] = useState(false); // Keeping for future fullscreen functionality
  const panelContentRef = useRef<HTMLDivElement>(null);

  // For OverviewPanel, we don't apply column filters to itself
  // const effectiveColumnSelection = isOverviewPanel
  //   ? { indices: [], headers: [] } // Empty selection for OverviewPanel
  //   : selectedColumns; // Use the global selection for other panels

  const effectiveColumnSelection =  selectedColumns; //Directly passed selectedColumns as checkboes were not reflectinng in the overview panel

  const renderComponent = () => {
    switch (data.type) {
      case ComponentType.TimeSeriesPanel:
        return (
          <TimeSeriesPanel
            data={fileData}
            selectedRows={selectedRows}
            selectedColumns={effectiveColumnSelection}
            dateFilter={dateFilter}
            panelFilters={panelFilters}
            onZoomSelection={onZoomSelection}
          />
        );
      case ComponentType.OverviewPanel:
        return (
          <OverviewPanel
            data={fileData}
            selectedRows={selectedRows}
            selectedColumns={effectiveColumnSelection}
            dateFilter={dateFilter}
            onRowSelection={onRowSelection}
            onColumnSelection={onColumnSelection}
            onDateFilterChange={onDateFilterChange}
          />
        );
      case ComponentType.HistogramPanel:
        return (
          <HistogramPanel
            data={fileData}
            selectedRows={selectedRows}
            selectedColumns={effectiveColumnSelection}
            dateFilter={dateFilter}
            panelFilters={panelFilters}
            onZoomSelection={onZoomSelection}
          />
        );
      case ComponentType.DataTablePanel:
        return (
          <DataTablePanel
            data={fileData}
            selectedRows={selectedRows}
            selectedColumns={effectiveColumnSelection}
            dateFilter={dateFilter}
            panelFilters={panelFilters}
            onRowSelection={onRowSelection}
            onColumnSelection={onColumnSelection}
            onDateFilterChange={onDateFilterChange}
          />
        );
      default:
        return <div>Unknown component type</div>;
    }
  };

  // const handleFullscreenToggle = (e: React.MouseEvent) => {
  //   e.stopPropagation();
  //   setIsFullscreen(!isFullscreen);
  // };

  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    console.log('Remove button clicked for:', data.id);
    onRemove(data.id);
  };

  // Create a configuration object for the panel options menu
  const getPanelConfiguration = () => {
    return {
      panelFilters:{
        selectedRows,
        selectedColumns,
        dateFilter}
    };
  };

  return (
    <div className={`grid-item ${isFullscreen ? 'fullscreen' : ''}`}>
      <div className="grid-item-header">
        <div className="grid-item-title drag-handle">{data.title}</div>
        <div className="grid-item-controls no-drag">
          <PanelOptionsMenu
            panelType={data.type}
            panelRef={panelContentRef}
            panelTitle={data.title}
            configuration={getPanelConfiguration()}
            fileId={file_id}
          />
          <button
            className="grid-item-control"
            title="Remove panel"
            onClick={handleRemove}
          >
            <CloseOutlined />
          </button>
        </div>
      </div>
      <div className="grid-item-content no-drag" ref={panelContentRef}>{renderComponent()}</div>
    </div>
  );
};

export default GridItem;
