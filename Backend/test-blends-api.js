// Test script to verify the blends API functionality
import fs from 'fs';
import path from 'path';
import csv from 'csv-parser';

const testCSVReading = async () => {
    console.log('Testing CSV reading functionality...');
    
    const csvPath = path.join(process.cwd(), 'uploads', 'blends_1.csv');
    console.log('CSV Path:', csvPath);
    
    if (!fs.existsSync(csvPath)) {
        console.error('❌ CSV file not found at:', csvPath);
        return;
    }
    
    console.log('✅ CSV file exists');
    
    const results = [];
    
    return new Promise((resolve, reject) => {
        fs.createReadStream(csvPath)
            .pipe(csv())
            .on('data', (data) => {
                // Transform the data to match the required format
                const transformedData = {
                    datetime: data.CreatedOn,
                    batch_id: data['Batch Id'],
                    L_color_value: parseFloat(data.L_color_value) || 0,
                    R_color_value: parseFloat(data.C_color_value) || 0, // Using C_color_value as R_color_value
                    C_color_value: parseFloat(data.H_color_value) || 0, // Using H_color_value as C_color_value
                    moist: parseFloat(data.Moist) || 0,
                    ffa: parseFloat(data.FFA) || 0,
                    iv: parseFloat(data.IV) || 0,
                    ph: parseFloat(data.PH) || 0,
                    fat: parseFloat(data.Fat) || 0,
                    cluster: data.Cluster_prev || 'Unknown',
                    quality: parseInt(data.quality) || 0
                };
                results.push(transformedData);
            })
            .on('end', () => {
                console.log(`✅ Successfully parsed ${results.length} records`);
                console.log('Sample record:', JSON.stringify(results[0], null, 2));
                
                // Test random selection
                const randomRecords = getRandomRows(results, 5);
                console.log(`✅ Random selection working - got ${randomRecords.length} records`);
                
                resolve(results);
            })
            .on('error', (error) => {
                console.error('❌ Error reading CSV:', error);
                reject(error);
            });
    });
};

// Function to get random rows from CSV data
const getRandomRows = (data, count = 5) => {
    if (data.length <= count) {
        return data;
    }
    
    const shuffled = [...data].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
};

// Run the test
testCSVReading()
    .then(() => {
        console.log('✅ All tests passed!');
        process.exit(0);
    })
    .catch((error) => {
        console.error('❌ Test failed:', error);
        process.exit(1);
    });
