import { generateResponse } from "../utils/commonResponse.js";

import User from '../models/user.model.js'
import { sequelizer } from '../database/dbConnection.js'
import { generateToken } from "../middlewares/jwt.js";
import ActivityLog from "../models/activityLogs.model.js";
import Roles from "../models/roles.model.js";

const loginUser = async (req, res) => {

  // console.log("user login controller")

  const { email, password } = req.body;
  if (!email) {
    return generateResponse(res, 400, 'email is required')
  }
  if (!password) {
    return generateResponse(res, 400, 'password is required')
  }

  try {
    const existingUser = await User.findOne({
      attributes: ['id', 'first_name', 'last_name', 'email', 'password_hash','onboarding','tenant_id', 'selected_systems'],
      where: {
        email: email  // Filter by email
      },
      include: [
        {
          model: Roles,
          as: 'roles',
          attributes: ['name', 'alias' ,'rank'] 
        },
      ]
    });
    if (!existingUser) {
      return generateResponse(res, 404, 'user not found')
    }
    if (password != existingUser.get('password_hash')) {
      return generateResponse(res, 404, 'email or password is invalid')
    }

    const userData = {
      first_name: existingUser.get('first_name'),
      last_name: existingUser.get('last_name'),
      email: existingUser.get('email'),
      id:existingUser.get('id'),
      onboarding:existingUser.get('onboarding'),
      tenant_id:existingUser.get('tenant_id'),
      selected_systems:existingUser.get('selected_systems'),
      role:existingUser.get('roles')
    }

    const token = generateToken(userData)
    if(!token){
      return generateResponse(res, 404, 'unable to generate token')
    }
    userData.token = token

    const newEntry = { user_id: userData.id, last_login: new Date() };

    const activityLog = new ActivityLog(newEntry);
    await activityLog.save();
    res.cookie('token', token, { httpOnly: false, secure: false });
    return generateResponse(res, 200, 'User logged In ',userData)

  } catch (error) {
    console.error('Error fetching users:', error);
    return generateResponse(res, 500, 'Internal server error')
  }

}

const updateUserOnboardingStatus = async (req, res) =>{
  const { status } = req.body;
  const {id} = req.user
  const {userId} = req.params
  if(!status){
    return generateResponse(res, 400, 'Status is required')
  }
  if(id != userId){
    return generateResponse(res, 401, 'You are not authorized to update the status of this user.')
  }
  try {
    const updatedUser = await User.update(
      { onboarding: status }, 
      {
        where: { id: id }, 
        returning: true,
      }
    )
    if(!updatedUser){
      return generateResponse(res, 404, 'User with the provided ID not found')
    }

    let user={
      email: updatedUser[1][0].email,
      is_active: updatedUser[1][0].is_active,
      onboarding: updatedUser[1][0].onboarding
    }
    return generateResponse(res, 200, 'User onboarding status updated successfully',user)

  } catch (error) {
  console.log('error :', error);
  return generateResponse(res, 500, 'Internal server error')
  }

}

const updateUserSelectedSystems = async (req, res) =>{
  const systems = Array.isArray(req.body.selected_systems) ? req.body.selected_systems: JSON.parse(req.body.selected_systems || '[]');
  let systemIds = [];
  if(systems && systems.length){
    systemIds = systems.map(item => item.systemId);
  }

  const {id} = req.params
  if(!systemIds){
    return generateResponse(res, 400, 'Selected systems are required')
  }
  try {
    const updatedUser = await User.update(
      { selected_systems: systemIds }, 
      {
        where: { id: id }, 
        returning: true,
      }
    )
    
    if(!updatedUser){
      return generateResponse(res, 404, 'User with the provided ID not found')
    }

    let user={
      email: updatedUser.email,
      selected_systems: updatedUser.selected_systems
    }
    return generateResponse(res, 200, 'User selected systems updated successfully',user)

  } catch (error) {
  console.log('error :', error);
  return generateResponse(res, 500, 'Internal server error')
  }
}

const usersList = async (req ,res) =>{
  try {

    const users = await User.findAll({
      attributes: ['id', 'first_name', 'last_name', 'email' , 'tenant_id'],
      where:{
        is_active : true,
        tenant_id: req.user.tenant_id
      },
      order: [['created_at', 'DESC']], 
    })

    return generateResponse(res , 200 , 'Users list fetched successfully' , users)
    
  } catch (error) {
    return generateResponse(res, 500, 'Internal server error')
  }
}


export {
  loginUser,
  updateUserOnboardingStatus,
  updateUserSelectedSystems,
  usersList
}