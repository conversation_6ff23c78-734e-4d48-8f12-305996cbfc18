import fs from 'fs';
import path from 'path';
import csv from 'csv-parser';
import { generateResponse } from '../utils/commonResponse.js';

// Store CSV data in memory for better performance
let csvData = [];
let lastLoadTime = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes cache

// Function to load CSV data
const loadCSVData = () => {
    return new Promise((resolve, reject) => {
        const csvPath = path.join(process.cwd(), 'uploads', 'blends_1.csv');
        console.log('csvPath', csvPath)
        const results = [];

        if (!fs.existsSync(csvPath)) {
            reject(new Error('CSV file not found'));
            return;
        }

        fs.createReadStream(csvPath)
            .pipe(csv())
            .on('data', (data) => {
                // Transform the data to match the required format
                const transformedData = {
                    datetime: data.DateTime,
                    batch_id: data['Batch Id'],
                    L_color_value: parseFloat(data.L_color_value) || 0,
                    R_color_value: parseFloat(data.C_color_value) || 0, // Using C_color_value as R_color_value
                    C_color_value: parseFloat(data.H_color_value) || 0, // Using H_color_value as C_color_value
                };
                results.push(transformedData);
            })
            .on('end', () => {
                resolve(results);
            })
            .on('error', (error) => {
                reject(error);
            });
    });
};

// Function to get sequential datetime/batch_id with random color values
const getSequentialWithRandomColors = (data, count = 5) => {
    if (data.length === 0) {
        return [];
    }

    // Get sequential rows for datetime and batch_id
    const startIndex = Math.floor(Math.random() * Math.max(1, data.length - count));
    const sequentialRows = data.slice(startIndex, startIndex + count);

    // Create result with sequential datetime/batch_id but random color values
    return sequentialRows.map((sequentialRow) => {
        // Get random indices for color values
        const randomIndex1 = Math.floor(Math.random() * data.length);
        const randomIndex2 = Math.floor(Math.random() * data.length);
        const randomIndex3 = Math.floor(Math.random() * data.length);

        return {
            // Sequential data
            datetime: sequentialRow.datetime,
            batch_id: sequentialRow.batch_id,

            // Random color values from different rows
            L_color_value: data[randomIndex1].L_color_value,
            R_color_value: data[randomIndex2].R_color_value,
            C_color_value: data[randomIndex3].C_color_value,

            // Keep other values from sequential row
            // moist: sequentialRow.moist,
            // ffa: sequentialRow.ffa,
            // iv: sequentialRow.iv,
            // ph: sequentialRow.ph,
            // fat: sequentialRow.fat,
            // cluster: sequentialRow.cluster,
            // quality: sequentialRow.quality
        };
    });
};

// Controller to get random blend data
export const getRandomBlendData = async (req, res) => {
    try {
        const currentTime = Date.now();
        
        // Load data if cache is empty or expired
        if (csvData.length === 0 || (currentTime - lastLoadTime) > CACHE_DURATION) {
            csvData = await loadCSVData();
            // console.log('csvData', csvData)
            lastLoadTime = currentTime;
        }

        // Get sequential datetime/batch_id with random color values (default 5, can be customized via query param)
        const count = parseInt(req.query.count) || 5;
        const randomData = getSequentialWithRandomColors(csvData, count);
        // console.log('randomData', randomData)

        return generateResponse(res, 200, 'Random blend data fetched successfully', randomData, csvData);
    } catch (error) {
        console.error('Error fetching random blend data:', error);
        return generateResponse(res, 500, 'Error fetching blend data', null, error.message);
    }
};

// Controller to get all blend data with pagination
export const getAllBlendData = async (req, res) => {
    try {
        const currentTime = Date.now();
        
        // Load data if cache is empty or expired
        if (csvData.length === 0 || (currentTime - lastLoadTime) > CACHE_DURATION) {
            csvData = await loadCSVData();
            lastLoadTime = currentTime;
        }

        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;

        const paginatedData = csvData.slice(startIndex, endIndex);
        const totalRecords = csvData.length;
        const totalPages = Math.ceil(totalRecords / limit);

        const response = {
            data: paginatedData,
            pagination: {
                currentPage: page,
                totalPages,
                totalRecords,
                hasNext: page < totalPages,
                hasPrev: page > 1
            }
        };

        return generateResponse(res, 200, 'Blend data fetched successfully', response);
    } catch (error) {
        console.error('Error fetching blend data:', error);
        return generateResponse(res, 500, 'Error fetching blend data', null, error.message);
    }
};

// Controller to refresh CSV data cache
export const refreshBlendData = async (req, res) => {
    try {
        csvData = await loadCSVData();
        lastLoadTime = Date.now();
        
        return generateResponse(res, 200, 'Blend data cache refreshed successfully', {
            totalRecords: csvData.length,
            lastUpdated: new Date(lastLoadTime).toISOString()
        });
    } catch (error) {
        console.error('Error refreshing blend data:', error);
        return generateResponse(res, 500, 'Error refreshing blend data', null, error.message);
    }
};
