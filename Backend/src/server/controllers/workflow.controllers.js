import Workflow from '../models/workflow.model.js';  // Adjust the path as needed
import WorkflowStructure from '../models/workflowStructure.model.js';
import { sequelize } from "../database/dbConnection.js";
import WorkflowComponents from '../models/workflowComponents.model.js';
import Operations from '../models/operation.model.js';
import axios from 'axios';
import <PERSON><PERSON><PERSON>ob from '../models/MLjobs.model.js';
import MLJobData from '../models/MLJobData.model.js';
import CSVFilesSchema from '../models/file.model.js';
import GoldenValueData from '../models/goldenValueData.model.js';
import Folder from '../models/folder.model.js';
import workflowFilters from '../models/workflowFilters.model.js';
import User from '../models/user.model.js';
import { Op } from 'sequelize';
import { dummyResponse, DummyMlJobDataResponse, correlationDummyMlJobDataResponse } from '../utils/dummyResponse.js'
import mongoose from 'mongoose';
import WorkflowShared from '../models/workflowShared.model.js';
import WorkflowFilters from '../models/workflowFilters.model.js'; // Corrected import
import GlobalConfiguration from '../models/globalConfiguration.model.js';
import Systems from '../models/systems.model.js';
import { generateResponse } from '../utils/commonResponse.js';

// 1. Create a new Workflow
export const createWorkflow = async (req, res) => {
    const { user_id, name } = req.body;

    try {
        const systems = Array.isArray(req.body.systems) ? req.body.systems : JSON.parse(req.body.systems || '[]');
        let systemIds = [];
        if (systems && systems.length) {
            systemIds = systems.map(item => item.systemId);
        }

        const newWorkflow = await Workflow.create({
            user_id: req.user.id,
            folder_id: req.body.folder_id,
            name,
            systems_id: systemIds,
        });

        return res.status(201).json({
            message: "Workflow added successfully.",
            data: newWorkflow
        });
    } catch (error) {
        console.error(error);
        return res.status(500).json({ error: 'Failed to create workflow' });
    }
};

export const getWorkflow = async (req, res) => {
    const { id } = req.user
    try {
        const workflows = await Workflow.findAll({
            where: { user_id: id },
            order: [
                ['id', 'DESC']
            ]
        })
        return res.status(200).json({ "message": "successfully Fetched", data: workflows });
    } catch (error) {
        console.error('Error fetching workflows:', error);
        return res.status(500).json({ error: 'Failed to retrieve workflows' });
    }
};




// 2. Update an existing Workflow
export const updateWorkflow = async (req, res) => {
    const { id } = req.params;
    const { tenant_id, user_id, name } = req.body;

    try {
        // Find the workflow by ID
        const workflow = await Workflow.findByPk(id);

        if (!workflow) {
            return res.status(404).json({ error: 'Workflow not found' });
        }

        // Update the workflow
        workflow.tenant_id = tenant_id || workflow.tenant_id;
        workflow.user_id = user_id || workflow.user_id;
        workflow.name = name || workflow.name;
        workflow.updated_at = new Date();

        await workflow.save();

        return res.status(200).json(workflow);
    } catch (error) {
        console.error(error);
        return res.status(500).json({ error: 'Failed to update workflow' });
    }
};


// WorkflowStructure Api
export const createWorkflowStructure = async (req, res) => {
    const { workflow_id, hierarchy } = req.body;

    try {
        if (!workflow_id) {
            return res.status(400).json({ error: 'workflow_id is required' });
        }

        const newWorkflowStructure = await WorkflowStructure.create({
            workflow_id,
            hierarchy,
        });

        return res.status(201).json(newWorkflowStructure);
    } catch (error) {
        console.error('Error creating workflow structure:', error);
        return res.status(500).json({ error: 'Failed to create workflow structure' });
    }
};

export const updateWorkflowStructure = async (req, res) => {
    const { id } = req.params;
    const { workflow_id, hierarchy } = req.body;

    try {
        const workflowStructure = await WorkflowStructure.findByPk(id);

        if (!workflowStructure) {
            return res.status(404).json({ error: 'Workflow structure not found' });
        }

        workflowStructure.workflow_id = workflow_id || workflowStructure.workflow_id;
        workflowStructure.hierarchy = hierarchy || workflowStructure.hierarchy;
        workflowStructure.updated_at = new Date();

        await workflowStructure.save();

        return res.status(200).json(workflowStructure);
    } catch (error) {
        console.error('Error updating workflow structure:', error);
        return res.status(500).json({ error: 'Failed to update workflow structure' });
    }
};


export const getWorkflowStructures = async (req, res) => {
    try {
        const workflowStructures = await WorkflowStructure.findAll({
            include: [
                {
                    model: Workflow,
                    attributes: ['id', 'name']
                }
            ]
        });

        if (!workflowStructures || workflowStructures.length === 0) {
            return res.status(404).json({ message: 'No workflow structures found' });
        }

        return res.status(200).json(workflowStructures);
    } catch (error) {
        console.error('Error fetching workflow structures:', error);
        return res.status(500).json({ message: 'Internal server error' });
    }
};


export const createWorkflowComponent = async (req, res) => {
    const { workflow_id, component, settings, type } = req.body;

    try {
        if (!workflow_id || !component || !type) {
            return res.status(400).json({ error: 'workflow_id, component, and type are required' });
        }

        const newComponent = await WorkflowComponents.create({
            workflow_id,
            component,
            settings,
            type,
            created_at: new Date(),
            updated_at: new Date(),
        });

        return res.status(201).json(newComponent);
    } catch (error) {
        console.error('Error creating workflow component:', error);
        return res.status(500).json({ error: 'Failed to create workflow component' });
    }
};

export const getOperations = async (req, res) => {
    try {
        const operations = await Operations.findAll(
            {
                where: {
                    isActive: true
                }
            }
        );
        // res.status(200).json(operations);
        return res.status(200).json({
            message: "Opertions fetched successfully.",
            data: operations
        });
    } catch (error) {
        console.error("Error fetching operations:", error);
        res.status(500).json({ error: "An error occurred while fetching operations." });
    }
};
const insertComponent = async (workflowComponents, newWorkflow, transaction) => {
    console.log('component')
    return new Promise(async (resolve, reject) => {
        console.log('component save')
        const newComponents = await Promise.all(workflowComponents.map(async (component) => {
            if (component.component == 'file') {
                component.settings = {
                    "exclude_features": [
                        "Boter_Lijn20_FI760A01",
                        "Toeren_BT001_SI715A02"
                    ],
                    "target_variable": "WORM_725ML007_TI725A13",
                    "feature_filters": {
                        "WORM_725ML005_TI725A04": {
                            "operator": "==",
                            "value": 10
                        },
                        "Date": {
                            "operator": "between",
                            "value": ["2024-05-01 00:00:00", "2024-05-10 23:59:59"]
                        }
                    }
                }
            }
            // if (component.type == 'file') {
            //     let flag = workflowComponents.find((k, v) => {
            //         console.log('--------->', k)
            //         return k.component == 'correlation'
            //     })
            //     if (component.settings.target_variable_settings && component.settings.target_variable_settings.rules && flag) {
            //         console.log('test====>00', component.settings.target_variable_settings)
            //         let field = component?.settings?.target_variable_settings?.rules[0]?.field
            //         if(!field) field = {}
            //         delete component.settings.target_variable_settings
            //         component.settings['target_variable'] = field
            //         console.log('component', component.settings)
            //     }
            // }
            console.log('component.component', component.type, Object.keys(component.settings).length)
            if (component.component == 'correlation' && Object.keys(component.settings).length === 0) {
                component.settings = {
                    "cross_correlation": false,
                }

                console.log('component save')

            }
            if (component.component == 'golden_batch') {
                console.log('component save2')

                // component.settings = {
                //     "clustering_model": "kmean123"
                // }
                component.settings = component.settings
                // await WorkflowComponents.create({
                //     // id: component.id,
                //     workflow_id: newWorkflow.id,
                //     component: 'rca',
                //     type: component.type,
                //     settings: {
                //         "feature_filters": {
                //             "date_column": {
                //                 "operator": "between",
                //                 "value": ["2024-05-01 00:00:00", "2024-05-10 23:59:59"]
                //             },
                //             "rca_runs": "True"
                //         }
                //     },
                //     created_at: component.created_at,
                //     updated_at: component.updated_at,
                // }, { transaction })
                // console.log('component save2')
            }
            if (component.component === 'rca' || component.component == 'golden_batch') {
                if (component.settings?.rca) {
                    if (component.settings.rca.deviation_threshold !== undefined) {
                        component.settings.rca.deviation_threshold /= 100;
                    }
                }
            }
            await WorkflowComponents.create({
                // id: component.id,
                workflow_id: newWorkflow.id,
                component: component.component,
                type: component.type,
                settings: component.settings,
                created_at: component.created_at,
                updated_at: component.updated_at,
            }, { transaction });

        }));
        resolve(newComponents)
    })
    // Insert data into WorkflowComponents table for each component

}
export const insertFullWorkflowData = async (req, res) => {
    const { workflow, workflowStructure, workflowComponents } = req.body;
    const { id } = req.user

    const transaction = await sequelize.transaction();

    try {
        // Insert data into Workflow table
        const newWorkflow = await Workflow.create({
            // id: workflow.id,
            // tenant_id: workflow.tenant_id,
            user_id: id,
            name: workflow.name,
            created_at: workflow.created_at,
            updated_at: workflow.updated_at,
        }, { transaction });

        // Insert data into WorkflowStructure table
        const newWorkflowStructure = await WorkflowStructure.create({
            // id: workflowStructure.id,
            workflow_id: newWorkflow.id,
            hierarchy: workflowStructure.hierarchy,
        }, { transaction });

        // Insert data into WorkflowComponents table for each component
        const newComponents = await insertComponent(workflowComponents, newWorkflow, transaction)
        console.log('newComponents========>', newComponents)

        // Commit the transaction if all inserts are successful
        await transaction.commit().catch(err => {
            console.log('err', err)
        });

        return res.status(201).json({
            message: "Workflow added successfully.",
            data: {
                workflow: newWorkflow,
                workflowStructure: newWorkflowStructure,
                workflowComponents: newComponents,
            }
        });
    } catch (error) {
        // Rollback the transaction if any insert fails
        await transaction.rollback();
        console.error('Error inserting full workflow data:', error);
        return res.status(500).json({ error: 'Failed to insert workflow data' });
    }
};

export const updateFullWorkflowData = async (req, res) => {
    const { workflow, workflowStructure, workflowComponents, systems } = req.body;
    const id = req.params.id;
    const userId = req.user.id
    // console.log('req.params',req.params)
    await WorkflowStructure.destroy({ where: { workflow_id: id } })
    await WorkflowComponents.destroy({ where: { workflow_id: id } })

    const transaction = await sequelize.transaction();
    // Update Workflow table
    const existingWorkflow = await Workflow.findByPk(id);
    if (!existingWorkflow) {
        return res.status(404).json({ error: 'Workflow not found' });
    }

    await existingWorkflow.update({
        tenant_id: workflow.tenant_id,
        // user_id: userId,
        // name: workflow.name,
        updated_at: workflow.updated_at,
    }, { transaction });
    await WorkflowStructure.create({
        workflow_id: existingWorkflow.id,
        hierarchy: workflowStructure.hierarchy
    }, { transaction })

    const newComponents = await insertComponent(workflowComponents, existingWorkflow, transaction)
    // await insertIntoWorkflowFilter(workflowComponents ,workflowStructure, id , userId , systems)


    // Commit the transaction if all inserts are successful
    await transaction.commit();

    return res.status(200).json({
        message: "Workflow updated successfully.",
        data: {
            workflow: existingWorkflow,
            workflowStructure: workflowStructure,
            workflowComponents: workflowComponents,
        }
    });

};

const insertIntoWorkflowFilter = async (workflowComponents, workflowStructure, workflowId, userId, systems) => {
    try {
        if (!workflowComponents) {
            return
        }
        let workflowSetting = {}
        workflowComponents?.forEach((res) => {
            let setting = {
                type: res.type,
                setting: res.settings
            }
            workflowSetting[res.type] = setting.setting;
        })
        const fileNode = workflowStructure?.hierarchy?.nodes.find(node => node?.data && node?.data?.type === 'file');
        const csvId = fileNode ? fileNode?.data?.id : null;

        let whereClause = `filters = '${JSON.stringify(workflowSetting)}' `

        const filteredData = await workflowFilters.findOne({
            where: sequelize.literal(whereClause),
            order: [['created_at', 'DESC']],
            attributes: ['id', 'name', 'workflow_id', 'created_at', 'version'],
        });

        if (!filteredData) {
            const lastWorkflowRecord = await workflowFilters.findOne({
                where: { workflow_id: workflowId },
                order: [['created_at', 'DESC']],
                attributes: ['id', 'name', 'workflow_id', 'created_at', 'version'],
            })
            const filters = await workflowFilters.create({
                name: lastWorkflowRecord?.dataValues ? workflowId + '_filter_' + (lastWorkflowRecord?.dataValues.version + 1) : `${workflowId}_filter`,
                workflow_id: workflowId,
                user_id: userId,
                csv_id: csvId,
                filters: workflowSetting,
                version: lastWorkflowRecord?.dataValues ? lastWorkflowRecord?.dataValues.version + 1 : 1,
                systems_name: systems?.names
            })
        }

        return true
    } catch (error) {
        console.log('error :', error);

        return true
    }
}

export const getWorkflowById = async (req, res) => {
    const { workflowId } = req.params;
    const { id, tenant_id } = req.user
    try {
        // Find the workflow by ID, including related WorkflowStructure and WorkflowComponents
        const workflow = await Workflow.findOne({
            where: { id: workflowId },
            include: [
                {
                    model: WorkflowStructure,
                    as: 'workflowStructure',
                    required: false, // Allows workflows without structure
                },
                {
                    model: WorkflowComponents,
                    as: 'workflowComponents',
                    required: false, // Allows workflows without components
                },
            ],
        });
        // Check if the workflow exists
        if (!workflow) {
            return res.status(404).json({ error: 'Workflow not found' });
        }

        let workflow_user_id = workflow.get('user_id')
        if (workflow_user_id == id) {
            return res.status(200).json({
                message: 'Workflow fetched successfully',
                data: workflow,
            });
        }

        const sharedWorkflow = await WorkflowShared.findOne({
            where: {
                share_type_id: workflowId,
                [sequelize.Op.or]: [
                    { shared_to_id: id },
                    { shared_to_id: tenant_id }
                ]
            },
            order: [['created_at', 'DESC']],
        })
        if (!sharedWorkflow) {
            return res.status(404).json({ error: 'Workflow not found' });
        }
        return res.status(200).json({
            message: 'Workflow fetched successfully',
            data: workflow,
            sharedWorkflowDetails: sharedWorkflow
        });

    } catch (error) {
        console.error('Error fetching workflow by ID:', error);
        return res.status(500).json({ error: 'Failed to fetch workflow' });
    }
};

export const executeWorkflow = async (req, res) => {
    const { workflowId } = req.params;

    try {
        // Find the workflow by ID, including related WorkflowStructure and WorkflowComponents
        const workflow = await Workflow.findOne({
            where: { id: workflowId },
            include: [
                {
                    model: WorkflowStructure,
                    as: 'workflowStructure',
                    required: false, // Allows workflows without structure
                },
                {
                    model: WorkflowComponents,
                    as: 'workflowComponents',
                    required: false, // Allows workflows without components
                },
            ],
        });

        // Check if the workflow exists
        if (!workflow) {
            return res.status(404).json({ error: 'Workflow not found' });
        }

        let payLoad = {
            workflow_id: workflowId,
            datasource: {
                file_path: 'liquor_proces.csv',
                settings: workflow.workflowComponents[0].settings
            }
        }
        console.log('workflow.workflowComponents[0].settings', workflow.workflowComponents[0].settings)

        console.log('workflow1111', workflow)
        return res.status(200).json({
            message: 'Workflow fetched successfully',
            workflow,
            payLoad
        });
    } catch (error) {
        console.error('Error fetching workflow by ID:', error);
        return res.status(500).json({ error: 'Failed to fetch workflow' });
    }
};


export const executeFlow = async (req, res) => {

    try {
        var golder_rca = {
            "workflow_id": 2,

            "datasource": {
                "file_path": "/OLAM/DATA/golden_batch_test_data.csv",
                "target_variable_settings": {
                    "h_y5": {
                        "operator": "between",
                        "value": [39.3, 42.3]
                    }
                },
                "exclude_features": [],
                "feature_filters": {

                }
            },

            "golden_cluster": {
                "clustering_model": "kmeans"
            },
            "rca": {
                "feature_filters":
                {

                },
                "rca_runs": true
            }


        }
        var payload_golden_cluster = {
            "workflow_id": 2,
            "datasource": {
                "file_path": "/OLAM/DATA/1731064575258.csv",
                "target_variable_settings": {
                    "WORM_725ML007_TI725A13": {
                        "operator": "between",
                        "value": [
                            5.4,
                            5.7
                        ]
                    }

                },
                "exclude_features": [
                    "Boter_Lijn20_FI760A01",
                    "Toeren_BT001_SI715A02"
                ],
                "feature_filters": {
                    "WORM_725ML005_TI725A04": {
                        "operator": "==",
                        "value": 5
                    },
                    "Date": {
                        "operator": "between",
                        "value": ["2024-05-01 00:00:00", "2024-05-10 23:59:59"]
                    }

                }
            },
            "golden_cluster": {
                "clustering_model": "kmean"
            },
            "rca": {
                "feature_filters": {
                    "date_column": {
                        "operator": "between",
                        "value": ["2024-05-01 00:00:00", "2024-05-10 23:59:59"]
                    },
                    "rca_runs": "True"
                }
            }
        }

        // const payload = {
        //     flow_id: 1,
        //     feature_filters: {},
        //     exclude_features: [],
        //     target_variable: "quality_overall",
        //     cross_correlation: false,
        //     file_path: "/home/<USER>/OLAM/DATA/1731064575258.csv"
        //   };
        const payload = {
            "workflow_id": 1,
            "datasource": {
                "file_path": "/OLAM/DATA/1731064575258.csv",
                "exclude_features": [
                    "Boter_Lijn20_FI760A01",
                    "Toeren_BT001_SI715A02"
                ],
                "target_variable": "WORM_725ML007_TI725A13",
                "feature_filters": {
                    "WORM_725ML005_TI725A04": {
                        "operator": "==",
                        "value": 10
                    },
                    "Date": {
                        "operator": "between",
                        "value": ["2024-05-01 00:00:00", "2024-05-10 23:59:59"]
                    }
                }
            },
            "correlation": {
                "cross_correlation": false,
            }
        }

        const response = await axios.post(
            'http://52.28.172.168/api/w/ofi/jobs/run/f/f/analytics_flow/correlations',
            payload,
            {
                headers: {
                    'Content-Type': 'application/json',
                    "Authorization": "Bearer leqS4ijeto0JMRuJNjleD2X8rOBHT3W9"
                }
            }
        );
        const response2 = await axios.post(
            // 'http://52.28.172.168/api/w/ofi/jobs/run/f/f/analytics_flow/golden_cluster',
            'http://52.28.172.168/api/r/golden_rca',
            golder_rca,
            {
                headers: {
                    'Content-Type': 'application/json',
                    "Authorization": "Bearer 1nESFenLzQvXhgvQ6py3rHvO0x7Ofnx0"
                }
            }
        );
        console.log('Response:', response.data, response2.data);

        let dataToSave = {
            workflow_id: 1,
            settings: payload,
            ml_response: response.data,
            status: "in_progress"
        }
        let dataToSave2 = {
            workflow_id: 2,
            settings: payload,
            ml_response: response2.data,
            status: "in_progress"
        }

        var newMljob = new MLJob(dataToSave);
        var savedData = await newMljob.save();

        var newMljob = new MLJob(dataToSave2);
        var savedData = await newMljob.save();
        console.log('savedData', savedData)

        return res.status(200).json({
            message: 'ML data fetched successfully',
            data: response.data,
        });
    } catch (error) {
        console.error('Error fetching workflow by ID:', error);
        return res.status(500).json({ error: 'Failed to fetch workflow' });
    }
};



export const executeWorkflowData = async (req, res) => {
    const { workflowId } = req.params;

    try {
        const workflow = await Workflow.findOne({
            where: { id: workflowId },
            include: [
                { model: WorkflowStructure, as: 'workflowStructure', required: false },
                { model: WorkflowComponents, as: 'workflowComponents', required: false },
            ],
        });

        if (!workflow) {
            return res.status(404).json({ error: 'Workflow not found' });
        }

        const operationComponent = await WorkflowComponents.findOne({
            where: { type: "operation", workflow_id: workflowId }
        });

        const fileComponent = await WorkflowComponents.findOne({
            where: { type: "file", workflow_id: workflowId }
        });

        if (!operationComponent || !fileComponent) {
            return res.status(404).json({ error: 'Required components not found' });
        }

        const csvId = fileComponent.component;
        const csvFileRecord = await CSVFilesSchema.findOne({
            where: { csv_id: csvId }
        });

        if (!csvFileRecord) {
            return res.status(404).json({ error: 'CSV file not found' });
        }

        // Transform the file settings into the desired structure

        let operationType = operationComponent?.dataValues?.component
        console.log('operationType', operationType)
        let inputMaterials
        // console.log('====operationComponent.component',operationComponent.component,operationComponent.settings)

        let payloadd = transformFileSettings(fileComponent.settings, operationType)
        console.log('payloadd', payloadd)

        // Generate ObjectId before making the API call
        const executionId = new mongoose.Types.ObjectId();

        let payLoad = {
            workflow_id: workflowId,
            execution_id: executionId.toString(), // Added execution_id to payload so that when updationg status of mljob we can update it using this id
            datasource: {
                file_path: csvFileRecord.aws_file_link,
                ...transformFileSettings(fileComponent.settings, operationType)
            },
        };
        if (operationComponent.component == 'golden_batch') {
            payLoad['golden_cluster'] = operationComponent.settings['golden_cluster']
            payLoad['rca'] = operationComponent?.settings?.rca && operationComponent.settings['rca']['rca_runs'] == 'true' ? { "rca_runs": true } : { "rca_runs": false }
            if (operationComponent?.settings && operationComponent.settings['rca']['deviation_threshold'])
                payLoad['rca']['deviation_threshold'] = operationComponent.settings['rca']['deviation_threshold']
            let globalSetting = {};
            fileComponent?.settings?.global_query?.rules.forEach(rule => {
                if (rule?.field) {
                    globalSetting[rule?.field] = {
                        operator: rule?.operator === '=' ? '==' : rule?.operator,
                        value: rule?.operator === 'between' ? rule?.value.split(',') : rule?.value
                    };
                }
            });
            payLoad.datasource.feature_filters = {
                ...payLoad.datasource.feature_filters,
                ...globalSetting
            };
            if (fileComponent?.settings?.dateFilter && fileComponent.settings.dateFilter.startDate != null) {
                payLoad.datasource.feature_filters = {
                    ...payLoad.datasource.feature_filters,
                    DateTime: {
                        operator: "between",
                        value: [fileComponent.settings.dateFilter.startDate, fileComponent.settings.dateFilter.endDate]
                    }
                };
            }

            console.log('req.user', req.user)
            const genericIdentifier = await attachedGenericIdentifier(req.user.tenant_id);
            console.log('genericIdentifier', genericIdentifier);

            const selectedSystems = req.user.selected_systems || [];
            const timeGeneralIdentifiers = selectedSystems.map(systemId => {
                const systemName = Object.keys(genericIdentifier).find(key => genericIdentifier[key].systemId == systemId);
                return systemName ? genericIdentifier[systemName].TimeGeneralIdentifiers : null;
            }).filter(identifier => identifier !== null).flat()
                .filter(identifier => identifier !== 'DateTime');

            console.log('timeGeneralIdentifiers', timeGeneralIdentifiers);
            if (timeGeneralIdentifiers) {
                payLoad.rca = {
                    ...payLoad.rca,
                    deviation_agg_columns: timeGeneralIdentifiers
                };
            }
            // console.log('operationComponent',operationComponent)
        }
        else if (operationComponent.component == 'correlation') {
            if (operationComponent.settings['correlation'] && operationComponent.settings['correlation'].cross_correlation == 'true') {
                operationComponent.settings['correlation'].cross_correlation = true
            } else {
                operationComponent.settings['correlation'].cross_correlation = false
            }
            payLoad[operationComponent.component] = operationComponent.settings['correlation']

        }
        else if (operationComponent.component == 'rca') {
            if (operationComponent.settings?.rca?.cluster_run_data) {
                let goldenValue = operationComponent.settings?.rca?.cluster_run_data
                let goldenDataResponse = await GoldenValueData.findOne({
                    where: { id: goldenValue }
                })
                if (goldenDataResponse?.dataValues?.golden_run_data) {
                    operationComponent.settings.rca.golden_run_data = goldenDataResponse?.dataValues?.golden_run_data
                    delete operationComponent.settings?.rca?.cluster_run_data
                }
                if (!goldenDataResponse?.dataValues?.golden_run_data && goldenDataResponse.dataValues.golden_value) {
                    const result = goldenDataResponse.dataValues.golden_value.reduce((acc, item) => {
                        acc[item.key] = {
                            value: item.golden_value
                        };
                        return acc;
                    }, {});
                    operationComponent.settings.rca.golden_run_data = result
                    delete operationComponent.settings?.rca?.cluster_run_data
                }
                operationComponent.settings
                payLoad.rca = operationComponent.settings.rca

                if (operationComponent?.settings && operationComponent.settings['rca']['deviation_threshold'])
                    payLoad['rca']['deviation_threshold'] = operationComponent.settings['rca']['deviation_threshold']

                // let settings = goldenDataResponse.dataValues.settings.datasource;
                // payLoad.datasource = settings
                // payLoad.datasource.file_path = csvFileRecord.aws_file_link
            }
            if (operationComponent.settings?.rca?.custom_cluster_run_data) {
                let goldenValue = operationComponent.settings?.rca?.custom_cluster_run_data
                let goldenDataResponse = await GoldenValueData.findOne({
                    where: { id: goldenValue }
                })
                if (goldenDataResponse?.dataValues?.golden_run_data) {
                    payLoad.rca.custom_golden_run_data = goldenDataResponse?.dataValues?.golden_run_data
                }
                if (!goldenDataResponse?.dataValues?.golden_run_data && goldenDataResponse.dataValues.golden_value) {
                    const result = goldenDataResponse.dataValues.golden_value.reduce((acc, item) => {
                        acc[item.key] = {
                            value: parseInt(item.golden_value, 10)
                        };
                        return acc;
                    }, {});
                    payLoad.rca.custom_golden_run_data = result
                }

                // let settings = goldenDataResponse.dataValues.settings.datasource;
                // payLoad.datasource = settings
                // payLoad.datasource.file_path = csvFileRecord.path_for_aiml
            }

            if (fileComponent?.settings?.dateFilter && fileComponent.settings.dateFilter.startDate != null) {
                payLoad.datasource.feature_filters = {
                    ...payLoad.datasource.feature_filters,
                    DateTime: {
                        operator: "between",
                        value: [fileComponent.settings.dateFilter.startDate, fileComponent.settings.dateFilter.endDate]
                    }
                };
            }

            // To remove unnecessary data from payload
            delete payLoad?.rca?.name
            delete payLoad?.rca?.custom_rca_runs
            delete payLoad?.rca?.custom_deviation_threshold
            delete payLoad?.rca?.custom_cluster_run_data
            delete payLoad?.rca?.custom_name
            delete payLoad?.rca?.custom_id
            delete payLoad?.rca?.id

        }
        else if (operationComponent.component == 'identification_model') {
            payLoad['datasource']['file_save_path'] = csvFileRecord.aws_file_link.replace(/\/+[^\/]+$/, `/model`);; //this will replace last uuid with /model+
            const updatedWorkflowFilePath = await Workflow.update(
                { file_saved_path_for_aiml: payLoad['datasource']['file_save_path'] || null }, // Set the new values to update
                { where: { id: workflowId } } // Specify the condition for update
            );
            payLoad['operation'] = operationComponent.component
            payLoad['clustering'] = operationComponent.settings['clustering']
            payLoad['dimensionality_reduction'] = operationComponent.settings['dimensionality_reduction']

            const genericIdentifier = await attachedGenericIdentifier(req.user.tenant_id);
            inputMaterials = Object.entries(genericIdentifier).reduce((acc, [key, value]) => {
                if (value.InputMaterial) {
                    acc[key] = value.InputMaterial;
                }
                return acc;
            }, {});
            // if (fileComponent?.settings?.dateFilter) {
            //     payLoad.datasource.feature_filters = {
            //         ...payLoad.datasource.feature_filters,
            //         DateTime: {
            //             operator: "between",
            //             value: [fileComponent.settings.dateFilter.startDate, fileComponent.settings.dateFilter.endDate]
            //         }
            //     };
            // }
        }
        else if (operationComponent.component === 'prediction_model') {
            let inputArray = operationComponent.settings['predictionModel']['pieceIdParameters'];

            // Add pieceIdParameters to the payload
            inputArray.push({
                "column_name": "Piece id",
                "value": operationComponent.settings['predictionModel']['pieceId']
            });
            // Add section to the payload
            inputArray.push({
                "column_name": "Section",
                "value": operationComponent.settings['predictionModel']['section']
            });

            // Add semis to the payload
            inputArray.push({
                "column_name": "Semis",
                "value": operationComponent.settings['predictionModel']['semis']
            });

            // Add beam type to the payload
            inputArray.push({
                "column_name": "Beam_type",
                "value": operationComponent.settings['predictionModel']['beamTypes']
            });

            // Add weight to the payload
            inputArray.push({
                "column_name": "weight",
                "value": operationComponent.settings['predictionModel']['weight']
            });

            // Add cast grade to the payload
            inputArray.push({
                "column_name": "Cast Grade",
                "value": operationComponent.settings['predictionModel']['castGrade']
            });

            payLoad['datasource']['input_parameters'] = inputArray;
            payLoad['datasource']['operation'] = 'prediction_model'
        }

        console.log('process.env.AIML_API', process.env.AIML_API)
        console.log('payLoaddddd', payLoad)
        let response
        if (!process.env.AIML_API || process.env.AIML_API == '') {

            console.log('if condition for dummy response :',);
            response = dummyResponse
        } else {
            let endpoint = '';
            switch (operationComponent.component) {
                case 'golden_batch':
                    endpoint = 'golden_rca';
                    break;
                case 'rca':
                    endpoint = 'golden_run_deviations';
                    break;
                case 'identification_model':
                    endpoint = 'input_clustering_train';
                    break;
                case 'prediction_model':
                    endpoint = 'rolling_ai_v1';
                    break;
                default:
                    endpoint = operationComponent.component;
            }

            response = await axios.post(
                `${process.env.AIML_API}/${endpoint}`,
                payLoad,
                {
                    headers: {
                        'Content-Type': 'application/json',
                        "Authorization": `Bearer ${operationComponent.component == 'correlations' ? process.env.AIML_CORRELATION_API_TOKEN : process.env.AIML_GOLDEN_BATCH_API_TOKEN}`
                    }
                }
            );
        }
        // console.log('response', response.data)

        // console.log('payLoad', payLoad);
        if (!response.data) return

        let filterId = await Workflow.findOne({
            where: { id: workflowId },
            order: [['created_at', 'DESC']],
            attributes: ['filter_id'], // Only retrieve the 'id'
        });

        filterId = filterId ? filterId.filter_id : null;
        // console.log('filterIddddd', filterId)


        let dataToSave = {
            _id: executionId,
            workflow_id: workflowId,
            settings: payLoad,
            ml_response: response.data,
            status: "in_progress",
            user_id: req.user.id,
            executed_by: req.user.first_name + ' ' + req.user.last_name,
            file_name: csvFileRecord.file_name,
            actual_settings: fileComponent.settings
        }
        if (filterId) {
            dataToSave.filter_id = filterId
        }
        if (inputMaterials) {
            dataToSave.inputMaterials = inputMaterials
        }

        var newMljob = new MLJob(dataToSave);
        var savedData = await newMljob.save();
        // console.log('savedData', savedData)

        if (!process.env.AIML_API || process.env.AIML_API == '') {

            console.log('operationComponent.component operationComponent.component operationComponent.component :', operationComponent.component);
            insertDummyMLJOBdata(workflowId, operationComponent.component)
        }
        return res.status(200).json({
            message: 'Workflow fetched successfully',
            data: savedData,
            payLoad
        });

    } catch (error) {
        console.error('Error fetching workflow by ID:', error.message);
        return res.status(500).json({ error: 'Failed to fetch workflow' });
    }
};


export const deleteWorkflow = async (req, res) => {
    const { id, type } = req.params;

    try {
        if (type == 'file') {
            const workflow = await Workflow.findByPk(id);

            if (!workflow) {
                return res.status(404).json({ error: 'Workflow not found' });
            }
            const result = await MLJob.deleteMany({ workflow_id: id });
            const mljobdata = await MLJobData.deleteMany({ workflow_id: id });

            await workflow.destroy();

            return res.status(200).json({ message: 'Workflow deleted successfully' });
        }
        else if (type == 'folder') {
            const folder = await Folder.findByPk(id);

            if (!folder) {
                return res.status(404).json({ error: 'Folder not found' });
            }

            await folder.destroy();

            return res.status(200).json({ message: 'Folder deleted successfully' });
        }

    } catch (error) {
        console.error('Error deleting workflow:', error);
        return res.status(500).json({ error: 'Failed to delete' });
    }
};

export const updateColumnsOrder = async (req, res) => {

    try {
        const workflowId = req.body.workflowId; // Specify the workflow ID
        const workflowColumns = await Workflow.update(
            {
                columns_order: req.body.columns, // Update only the columns_order field
            },
            {
                where: {
                    id: workflowId, // Ensure the update is applied to the specific workflow ID
                },
                returning: true, // Return the updated record(s)
            }
        );
        return res.status(200).json({ message: 'Run Deviation Table Columns Saved' });

    } catch (error) {
        console.error('Error run deviation table columns:', error);
        return res.status(500).json({ error: 'Failed to update workflow run deviation columns' });
    }
};

export const getConnectedTenants = async (req, res) => {
    try {
        const currentUser = await User.findOne({
            where: { id: req.user.id },
        });

        if (!currentUser) {
            return res.status(404).json({ error: 'User not found' });
        }

        // Find all users with the same tenant_id
        // console.log('currentUser', currentUser)
        const tenants = await User.findAll({
            where: {
                tenant_id: currentUser.tenant_id,
                // id: { [Op.ne]: req.user.id }, // Exclude the current user
            },
        });

        if (!tenants || tenants.length === 0) {
            return res.status(404).json({ error: 'Tenants not found' });
        }

        return res.status(200).json({
            message: 'Tenants fetched successfully',
            data: tenants,
        });
    } catch (error) {
        console.error('Error fetching workflow by ID:', error);
        return res.status(500).json({ error: 'Failed to fetch tenants' });
    }
};

export const getWorkflowRuns = async (req, res) => {
    const { workflowId, page = 1, size = 10, range, searchFile } = req.body;
    const skip = (page - 1) * size;

    try {
        // Check if the workflow is owned by the current user
        const workflow = await Workflow.findOne({
            where: { id: workflowId }
        });

        let filter = { workflow_id: workflowId };
        let isSharedWorkflow = false;

        // If the workflow is not owned by current user, it might be a shared workflow
        if (workflow && workflow.user_id !== req.user.id) {
            // Check if this is a shared workflow with 'runs' access level
            const sharedWorkflowEntry = await WorkflowShared.findOne({
                where: {
                    share_type: 'workflow',
                    share_type_id: workflowId,
                    shared_to_id: req.user.id,
                    access_level: 'runs'
                }
            });

            if (sharedWorkflowEntry) {
                isSharedWorkflow = true;

                // Find all shared run entries for this user and workflow
                const sharedRunEntries = await WorkflowShared.findAll({
                    where: {
                        share_type: 'runs',
                        shared_to_id: req.user.id
                    },
                    attributes: ['share_type_id']
                });

                if (sharedRunEntries && sharedRunEntries.length > 0) {
                    // Extract the shared run IDs
                    const sharedRunIds = sharedRunEntries.map(entry => entry.share_type_id);

                    // Only fetch runs that have been shared with this user
                    filter = {
                        _id: { $in: sharedRunIds },
                        workflow_id: workflowId
                    };
                } else {
                    // If no specific runs are shared, return empty result
                    return res.status(200).json({
                        message: "No shared runs found",
                        data: [],
                        total: 0,
                        page,
                        size
                    });
                }
            }
        }

        // Add date range and search filters
        if (range?.start && range?.end) {
            filter.created_at = { $gte: new Date(range.start), $lte: new Date(range.end) };
        }

        if (searchFile && searchFile.length > 0) {
            filter.file_name = { $regex: new RegExp(searchFile, 'i') };
        }

        const workflowRuns = await MLJob.find(filter)
            .sort({ _id: -1 })
            .skip(skip)
            .limit(size);

        const total = await MLJob.countDocuments(filter);
        console.log('totalll', total)

        return res.status(200).json({
            message: isSharedWorkflow ? "Shared workflow runs fetched successfully" : "Workflow runs fetched successfully",
            data: workflowRuns,
            total,
            page,
            size
        });

    } catch (error) {
        console.error('Error fetching workflow runs:', error);
        return res.status(500).json({ error: 'Failed to fetch workflow runs' });
    }
};


export const transformFileSettings = (settings, operationType) => {
    const { rules, target_variable_settings, exclude_features, global_query, ...rest } = settings;


    // Transform target variable settings
    let transformedTargetSettings = {};
    let targetVariable
    if (target_variable_settings?.rules) {
        target_variable_settings.rules.forEach(rule => {
            if (rule.field) {
                transformedTargetSettings[rule.field] = {
                    operator: rule.operator === '=' ? '==' : rule.operator,
                    value: rule.operator === 'between' ? rule.value.split(',') :
                        rule.value
                };
            }
        });
    } else if (target_variable_settings) {
        transformedTargetSettings = target_variable_settings
    }
    if (settings.target_variable) {
        targetVariable = settings.target_variable
    }

    // Transform feature filters
    const featureFilters = {};
    if (rules) {
        rules.forEach(rule => {
            if (rule.field) {
                featureFilters[rule.field] = {
                    operator: rule.operator === '=' ? '==' : rule.operator,
                    value: rule.operator === 'between' ? rule.value.split(',') :
                        rule.value
                };
            }
        });
    }
    // // c;onst featureFilters = {}
    if (operationType == 'rca') {
        if (global_query) {
            global_query?.rules?.forEach(rule => {
                if (rule.field) {
                    featureFilters[rule.field] = {
                        operator: rule.operator === '=' ? '==' : rule.operator,
                        value: rule.operator === 'between' ? rule.value.split(',') :
                            rule.value
                    };
                }
            });
        }
    }
    let payloadData
    if (Object.keys(transformedTargetSettings).length > 0) {

        if (operationType == 'correlation') {
            payloadData = {
                target_variable: operationType == 'correlation' ? Object.keys(transformedTargetSettings)[0] : transformedTargetSettings,
                exclude_features: exclude_features,
                feature_filters: featureFilters
            }
        } else {
            payloadData = {
                target_variable_settings: operationType == 'correlation' ? Object.keys(transformedTargetSettings)[0] : transformedTargetSettings,
                exclude_features: exclude_features,
                feature_filters: featureFilters
            }
        }
        // payloadData = {
        //     target_variable_settings: operationType == 'correlation' ?  Object.keys(transformedTargetSettings)[0] : transformedTargetSettings,
        //     exclude_features: exclude_features,
        //     feature_filters: featureFilters
        // }
    } else {
        payloadData = {
            target_variable_settings: targetVariable ? targetVariable : {},
            exclude_features: exclude_features,
            feature_filters: featureFilters
        }

    }




    return payloadData;
};

const insertDummyMLJOBdata = async (workflowId, operation) => {
    try {
        const updatedJob = await MLJob.findOneAndUpdate(
            { workflow_id: workflowId },
            { status: 'completed' },
            { new: true }
        ).sort({ _id: -1 });
        if (!updatedJob) {
            console.log("MLJOB job status is not updated on enviornment")
            // return generateResponse(res, 404, 'MLJob with the specified workflow_id not found:')
        }
        let result
        if (operation == 'rca') {
            result = DummyMlJobDataResponse
        }
        if (operation == 'golden_batch') {
            result = DummyMlJobDataResponse
        }
        if (operation == 'correlation') {
            result = correlationDummyMlJobDataResponse
        }


        const mlJobData = new MLJobData({
            ml_job_id: updatedJob._id,
            result: result.workflowData,
            workflow_id: workflowId
        });

        await mlJobData.save();
        console.log("MLJOB job and MLJOBDATA status is  updated with dummy response")
        // return generateResponse(res, 200, 'MLJobData entry created successfully:')
    } catch (error) {
        console.log("error", error)
        // return generateResponse(res, 500, 'Error completing MLJob and saving result:')
    }
}

export const getInProgressWorkflowRuns = async (req, res) => {
    const { runId } = req.body;

    try {
        const run = await MLJob.findOne({ _id: runId });

        if (!run) {
            return res.status(404).json({
                message: "Run not found",
                data: null
            });
        }

        return res.status(200).json({
            message: "Run status fetched successfully",
            data: run
        });

    } catch (error) {
        console.error('Error fetching run status:', error);
        return res.status(500).json({ error: 'Failed to fetch run status' });
    }
};


export async function manipulateFilters() {
    try {
        console.log("logogogog");

        // Find all workflow components with component "golden_batch"
        const rcaWorkflows = await WorkflowComponents.findAll({
            where: {
                [Op.or]: [
                    { component: "golden_batch" },
                    { component: "corelation" }
                ]
            }
        });

        if (!rcaWorkflows || rcaWorkflows.length === 0) {
            console.log('No RCA workflows found');
            return;
        }

        // Extract workflow IDs from the found components
        const workflowIds = rcaWorkflows.map(workflow => workflow.workflow_id);
        console.log('workflowIds', workflowIds);

        // Find all workflow components with type "file" for the extracted workflow IDs
        const fileComponents = await WorkflowComponents.findAll({
            where: {
                workflow_id: workflowIds,
                type: "file"
            }
        });

        if (!fileComponents || fileComponents.length === 0) {
            console.log('No file components found for the given workflow IDs');
            return;
        }

        // Map the fileComponents and modify the JSON of fileComponents.settings
        const modifiedFileComponents = await Promise.all(fileComponents.map(async component => {
            const modifiedSettings = modifyJsonObject(component.settings);
            await WorkflowComponents.update(
                { settings: modifiedSettings },
                { where: { id: component.id } }
            );
            return {
                ...component,
                settings: modifiedSettings
            };
        }));

        console.log('modifiedFileComponents', modifiedFileComponents);
        return modifiedFileComponents;
    } catch (error) {
        console.log('error', error);
    }
}

function modifyJsonObject(jsonObject) {
    return {
        ...jsonObject,
        rules: [], // Clear the rules array
        global_query: {
            combinator: jsonObject.combinator,
            rules: jsonObject.rules // Move existing rules to global_query
        }
    };
}

export async function manipulateRCAFilters() {
    try {
        console.log("logogogog");

        const workflowFilters = await WorkflowFilters.findAll({
            where: {
                filter_type: null
                // id: 57
            }
        });

        if (!workflowFilters || workflowFilters.length === 0) {
            console.log('No workflow filters found');
            return;
        }

        // Map the workflowFilters and modify the JSON of workflowFilters.filters
        const modifiedWorkflowFilters = await Promise.all(workflowFilters.map(async filter => {
            const modifiedFilters = modifyRunJsonObject(filter.filters);
            console.log('modifiedFilters', modifiedFilters)
            await WorkflowFilters.update(
                { filters: modifiedFilters },
                { where: { id: filter.id } }
            );
            return {
                ...filter,
                filters: modifiedFilters
            };
        }));

        console.log('modifiedWorkflowFilters', modifiedWorkflowFilters);
        return modifiedWorkflowFilters;
    } catch (error) {
        console.log('error', error);
    }
}

function modifyRunJsonObject(jsonObject) {
    return {
        ...jsonObject,
        file: {
            ...jsonObject.file,
            global: {
                combinator: jsonObject.file.combinator,
                rules: jsonObject.file.rules // Move existing rules to global_query
            }
        }
    };
}


export async function insertSettingInRca() {
    try {
        const rcaWorkflows = await WorkflowComponents.findAll({
            where: {
                component: "rca",
                // workflow_id: 355
            }
        });

        if (!rcaWorkflows || rcaWorkflows.length === 0) {
            console.log('No RCA workflows found');
            return;
        }

        // const workflowIds = rcaWorkflows.map(workflow => workflow.workflow_id);
        // console.log('workflowIds', workflowIds);

        // Map the rcaWorkflows to get cluster_run_data and look into GoldenValueData table
        const modifiedRcaWorkflows = await Promise.all(rcaWorkflows.map(async workflow => {
            const clusterRunDataId = workflow.settings.rca.cluster_run_data;
            console.log('clusterRunDataId', clusterRunDataId)
            const goldenDataResponse = await GoldenValueData.findOne({
                where: { id: clusterRunDataId }
            });

            console.log('goldenDataResponse', goldenDataResponse)
            if (!goldenDataResponse) {
                console.log(`No GoldenValueData found for cluster_run_data ID: ${clusterRunDataId}`);
                return workflow;
            }

            console.log('GoldenValueData filters:', goldenDataResponse.filter_id);
            const filterData = await WorkflowFilters.findOne({ where: { id: goldenDataResponse.filter_id } })
            console.log('filterData', filterData.filters)

            const workflowFilter = filterData.filters
            console.log('workflowFilterrrrr', workflowFilter.file)

            workflowFilter.file.global_query = workflowFilter.file.global; // Rename `global` to `global_query`
            delete workflowFilter.file.global;
            console.log('workflowFilterrrrr1111', workflowFilter.file)

            console.log('workflow.id', workflow.workflow_id)
            const updated = await WorkflowComponents.update(
                { settings: workflowFilter.file },
                { where: { workflow_id: workflow.workflow_id, type: "file" } }
            );
            console.log('updatedddd', updated)

            return workflow;
        }));

        console.log('modifiedRcaWorkflows', modifiedRcaWorkflows);
        return modifiedRcaWorkflows;
    } catch (error) {
        console.log('error', error);
    }
}


export async function attachedGenericIdentifier(tenant_id) {
    try {

        const configuration = await GlobalConfiguration.findOne({
            where: { tenant_id },
            order: [['created_at', 'DESC']],
            attributes: ['configurations', 'created_at']
        });

        if (!configuration) {
            return generateResponse(res, 200, 'No configuration found', {});
        }

        const updatedConfigurations = { ...configuration.configurations };
        const updatedConfigsTemp = {};


        for (const systemKey in updatedConfigurations) {
            if (updatedConfigurations.hasOwnProperty(systemKey)) {
                const system = updatedConfigurations[systemKey];

                // If systemId exists, find the system name from the database
                if (system.systemId) {
                    let systemNameFromDb = await Systems.findOne({
                        where: { id: system.systemId },
                        attributes: ['name']
                    });

                    if (systemNameFromDb) {
                        systemNameFromDb = systemNameFromDb.dataValues.name;
                        updatedConfigsTemp[systemNameFromDb] = system
                    } else {
                        // No system name found for systemId
                        updatedConfigsTemp[systemKey] = system
                    }
                } else {
                    updatedConfigsTemp[systemKey] = system
                }
            }
        }

        return updatedConfigsTemp

    } catch (error) {
        console.error('error:', error);
        return generateResponse(res, 500, 'Failed to fetch configuration');
    }
}

export const saveInputParams = async (req, res) => {
    try {
        const { workflowId } = req.params
        const response = await WorkflowComponents.update(
            {
                settings: req.body,
                updated_at: new Date(), // you probably want to update the timestamp
            },
            {
                where: {
                    workflow_id: workflowId,
                    type: 'operation',
                },
            }
        );
        return generateResponse(res, 200, 'Input params saved successfully', response);
    } catch (error) {
        console.log('error :', error);
        return generateResponse(res, 500, 'Failed to store input params');
    }

}