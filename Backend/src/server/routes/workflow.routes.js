import express from 'express';
import { createWorkflow, createWorkflowComponent, createWorkflowStructure, executeFlow, executeWorkflow, executeWorkflowData, getOperations, getWorkflow, getWorkflowById, getWorkflowStructures, insertFullWorkflowData, updateFullWorkflowData, updateWorkflow, updateWorkflowStructure ,deleteWorkflow,updateColumnsOrder, getConnectedTenants, getWorkflowRuns, getInProgressWorkflowRuns, saveInputParams} from '../controllers/workflow.controllers.js'; // Adjust path as needed
import {authenticateToken} from '../middlewares/jwt.js'
const router = express.Router();

router.post('/create-workflows',authenticateToken , createWorkflow);




// router.put('/editWorkflows/:id', updateWorkflow);

// router.post('/workflow-structures', createWorkflowStructure);

// router.put('/workflow-structures/:id', updateWorkflowStructure);

// router.get('/get-workflowStructures', getWorkflowStructures);

// router.post('/create-workflow-components', createWorkflowComponent);

// ---------Operations
router.get('/operations', getOperations);
router.get('/connected-tenants',authenticateToken, getConnectedTenants);

// -------Workflow CRUD
router.get('/',authenticateToken, getWorkflow);
router.post('/',authenticateToken, insertFullWorkflowData);
router.put('/:id',authenticateToken, updateFullWorkflowData);
router.get('/:workflowId',authenticateToken, getWorkflowById);
router.delete('/:id/:type', authenticateToken, deleteWorkflow);

// ----------Execute Workflow
router.get('/:workflowId/execute', authenticateToken, executeWorkflowData);

router.post('/execute-workflow/:workflowId', executeWorkflow);
router.get('/execute-flow', executeFlow);
router.put('/rundeviation/columns-order',updateColumnsOrder)

router.post('/workflow-runs', authenticateToken, getWorkflowRuns)
router.post('/workflow-runs/in-progress', authenticateToken, getInProgressWorkflowRuns)
router.put('/input-params/:workflowId',saveInputParams)

export default router;
