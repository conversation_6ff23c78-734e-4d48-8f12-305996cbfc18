import { Router } from "express";
import {authenticateToken} from "../middlewares/jwt.js"
const router =Router()

// ********Import the controllers ******

import { loginUser ,updateUserOnboardingStatus,updateUserSelectedSystems , usersList} from "../controllers/user.controllers.js";



// ********Define path of controllers ******

router.route('/login').post(loginUser)
router.route('/:userId/status').patch(authenticateToken,updateUserOnboardingStatus)
router.route('/:id/update-selected-systems').patch(authenticateToken,updateUserSelectedSystems)
router.route('/list').get(authenticateToken, usersList)
// router.route('/register').post(upload.single('avtar'),registerUser)


export default router