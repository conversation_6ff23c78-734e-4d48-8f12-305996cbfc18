import {app} from './server/app.js'
import {connectDatabase,connectMongoDB} from './server/database/dbConnection.js'
import 'dotenv/config'

const port = process.env.PORT || 5001
try {
    await connectDatabase()
} catch (error) {
    console.log('Database connection  failled', error); 
}
try {
    await connectMongoDB()
} catch (error) {
    console.log('Database connection  failled', error); 
}

app.get('*', (req, res) => {
   // res.sendFile(path.join(__dirname, 'public', 'index.html'));
  });

  
app.listen(port,(err)=>{
    if(err){
        console.log('error while listning on port :', err);
    }else{
        console.log("app is listning on port",port)
    }
})
