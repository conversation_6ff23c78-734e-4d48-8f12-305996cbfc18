// Test script to verify the carbon black API functionality
import fs from 'fs';
import path from 'path';
import csv from 'csv-parser';

const testCarbonBlackCSVReading = async () => {
    console.log('Testing Carbon Black CSV reading functionality...');
    
    const csvPath = path.join(process.cwd(), 'uploads', 'CB_batches_formatted.csv');
    console.log('CSV Path:', csvPath);
    
    if (!fs.existsSync(csvPath)) {
        console.error('❌ Carbon Black CSV file not found at:', csvPath);
        return;
    }
    
    console.log('✅ Carbon Black CSV file exists');
    
    const results = [];
    
    return new Promise((resolve, reject) => {
        fs.createReadStream(csvPath)
            .pipe(csv())
            .on('data', (data) => {
                // Extract only IAN value and other relevant data
                const transformedData = {
                    IAN: parseFloat(data.IAN) || 0,
                    batch_id: data.Batch_id,
                    original_datetime: data.DateTime,
                };
                results.push(transformedData);
            })
            .on('end', () => {
                console.log(`✅ Successfully parsed ${results.length} records`);
                console.log('Sample record:', JSON.stringify(results[0], null, 2));
                
                // Test random selection
                const randomIndex = Math.floor(Math.random() * results.length);
                const randomRecord = results[randomIndex];
                
                console.log('✅ Random selection working');
                console.log('Random record:', JSON.stringify(randomRecord, null, 2));
                
                // Test current datetime generation
                const currentDateTime = new Date().toISOString();
                console.log('✅ Current datetime generation working:', currentDateTime);
                
                // Test final response format
                const responseData = {
                    datetime: currentDateTime,
                    IAN: randomRecord.IAN,
                    batch_id: randomRecord.batch_id
                };
                
                console.log('✅ Final response format:');
                console.log(JSON.stringify(responseData, null, 2));
                
                resolve(results);
            })
            .on('error', (error) => {
                console.error('❌ Error reading CSV:', error);
                reject(error);
            });
    });
};

// Run the test
testCarbonBlackCSVReading()
    .then(() => {
        console.log('✅ All Carbon Black API tests passed!');
        process.exit(0);
    })
    .catch((error) => {
        console.error('❌ Test failed:', error);
        process.exit(1);
    });
