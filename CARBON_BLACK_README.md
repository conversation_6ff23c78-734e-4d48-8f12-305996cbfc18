# Carbon Black IAN Prediction Component

This document describes the new Carbon Black IAN Prediction component that displays real-time IAN (Iodine Absorption Number) values from the `CB_batches_formatted.csv` file with automatic updates every 1 minute.

## Overview

The Carbon Black IAN Prediction component fetches random IAN values from the CB_batches_formatted.csv file and pairs them with the current datetime. The component displays this data in a table format with automatic refresh functionality.

### Key Features:
- **Random IAN Values**: Fetches random IAN values from the CSV file
- **Current DateTime**: Each entry uses the current system datetime
- **1-minute Intervals**: New entries are added every 1 minute
- **Real-time Updates**: Automatic refresh with pause/resume functionality
- **Data History**: Maintains up to 50 recent records

## Files Created/Modified

### Backend Files

1. **`Backend/src/server/controllers/blends.controller.js`** (modified)
   - Added `loadCarbonBlackData()` function to read CB_batches_formatted.csv
   - Added `getRandomCarbonBlackData()` controller function
   - Implements CSV caching for performance (5-minute cache)

2. **`Backend/src/server/routes/blends.routes.js`** (modified)
   - Added import for `getRandomCarbonBlackData`
   - Added route: `GET /carbon-random`

### Frontend Files

3. **`Frontend/src/components/Dashboard/CarbonDemoPrediction.tsx`** (created/updated)
   - Main component with 1-minute auto-refresh functionality
   - Displays data in Ant Design table format
   - Control panel for pause/resume and manual refresh

4. **`Frontend/src/components/Dashboard/CarbonDemoPrediction.css`** (created)
   - Styling for the component
   - Responsive design and animations
   - Professional table styling

### Test Files

5. **`Backend/test-carbon-api.js`** (created)
   - Test script to verify CSV reading and API functionality

## API Endpoints

### GET `/api/v1/blends/carbon-random`
Fetches a random IAN value from CB_batches_formatted.csv with current datetime.

**Response:**
```json
{
  "success": true,
  "message": "Random carbon black data fetched successfully",
  "data": {
    "datetime": "2025-09-18T06:15:49.702Z",
    "IAN": 147.7620492,
    "batch_id": "4563577"
  }
}
```

**Response Fields:**
- `datetime`: Current system datetime in ISO format
- `IAN`: Random IAN value from the CSV file
- `batch_id`: Corresponding batch ID from the CSV (for reference)

## Component Features

### Table Structure
| Column | Description | Format |
|--------|-------------|---------|
| DateTime | Current system time when data was fetched | MM/DD/YYYY HH:mm:ss |
| IAN | Random IAN value from CSV | Decimal with 2 places |

### Control Panel
- **Auto-refresh Toggle**: Start/pause automatic updates
- **Manual Refresh**: Trigger immediate data fetch
- **Status Indicator**: Shows current auto-refresh state
- **Loading Indicator**: Visual feedback during API calls

### Data Management
- **Maximum Records**: Displays up to 50 records
- **Newest First**: New entries appear at the top
- **Pagination**: Built-in pagination for easy navigation
- **Real-time Updates**: Live status and timestamp information

## Usage

### Accessing the Component

The component is available at:
```
Frontend/src/components/Dashboard/CarbonDemoPrediction.tsx
```

### Integration Example

```typescript
import CarbonDemoPrediction from './components/Dashboard/CarbonDemoPrediction';

// Use in your application
<CarbonDemoPrediction />
```

### Component Props

The component doesn't require any props and manages its own state internally.

## Configuration

### Refresh Interval
The component is configured to fetch new data every 1 minute (60,000 milliseconds). This can be modified in the component:

```typescript
const newIntervalId = setInterval(() => {
  fetchCarbonBlackData();
}, 60000); // Change this value to adjust interval
```

### Data Retention
The component keeps the last 50 records. This can be adjusted:

```typescript
return updatedData.slice(0, 50); // Change 50 to desired number
```

## Data Source

### CSV File Structure
The component reads from `Backend/uploads/CB_batches_formatted.csv` which contains:
- **360 records** of carbon black batch data
- **IAN column**: Contains Iodine Absorption Number values
- **Batch_id column**: Unique batch identifiers
- **DateTime column**: Original batch timestamps (for reference only)

### Sample Data
```csv
Batch_id,DateTime,IAN,...
4563324,2025-06-26 00:00:00,98.67719828,...
4563325,2025-06-26 01:00:00,140.9280257,...
```

## Testing

### Backend Testing
Run the test script to verify CSV reading and API functionality:

```bash
cd Backend
node test-carbon-api.js
```

**Expected Output:**
```
✅ Carbon Black CSV file exists
✅ Successfully parsed 360 records
✅ Random selection working
✅ Current datetime generation working
✅ Final response format working
✅ All Carbon Black API tests passed!
```

### Frontend Testing
1. Start the application
2. Navigate to the CarbonDemoPrediction component
3. Verify initial data load
4. Test auto-refresh functionality
5. Test manual refresh and pause/resume controls

## Performance Considerations

- **CSV Caching**: Data is cached in memory for 5 minutes to reduce file I/O
- **Limited Records**: Component displays maximum 50 records to prevent memory issues
- **Efficient Updates**: Only new data is fetched, existing data is preserved
- **Cleanup**: Proper interval cleanup on component unmount

## Error Handling

- **File Not Found**: Graceful handling if CSV file is missing
- **API Errors**: User-friendly error messages
- **Network Issues**: Retry mechanism and error display
- **Data Validation**: Ensures IAN values are valid numbers

## Troubleshooting

1. **No Data Displayed**: 
   - Check if CB_batches_formatted.csv exists in Backend/uploads/
   - Verify API endpoint is accessible
   - Check browser console for errors

2. **Auto-refresh Not Working**:
   - Ensure auto-refresh is enabled (green status)
   - Check for JavaScript errors in console
   - Verify API is responding correctly

3. **Performance Issues**:
   - Reduce refresh interval if needed
   - Clear browser cache
   - Check server resources

## Future Enhancements

- Add data filtering and search capabilities
- Implement data export functionality
- Add charts and visualizations for IAN trends
- Support for multiple CSV files
- Real-time notifications for specific IAN ranges
- Historical data analysis features
