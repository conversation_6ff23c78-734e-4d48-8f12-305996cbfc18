name: CI & Deploy

on:
  #push:
  #  branches:
  #    - rolling-steel-implementation-ai
  workflow_dispatch:

jobs:
  build-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Node
        uses: actions/setup-node@v3
        with:
          node-version: 18

      #- name: Install and build frontend (build copies to backend)
      #  working-directory: ./Frontend
      #  run: |
      #    npm install
      #    npm run build

      - name: Deploy with Docker Compose (Staging/Prod)
        uses: appleboy/ssh-action@v1
        with:
          host: ${{ secrets.HOST }}
          username: ec2-user
          key: ${{ secrets.SSH_KEY }}
          script: |
            cd /home/<USER>/docker/process_sw
            git pull origin rolling-steel-implementation-ai
            docker compose down
            docker compose up -d --build
