# Blends Prediction Table Component

This document describes the new Blends Prediction Table component that displays real-time data from the `blends_1.csv` file with automatic updates every 10 seconds.

## Overview

The Blends Prediction Table component fetches data from the CSV file with a unique logic:
- **Sequential Data**: Datetime and Batch ID are fetched sequentially from consecutive rows
- **Random Prediction Values**: L_color_value, R_color_Value, and C_color_value are randomly selected from different rows

The table displays the following columns:
- **Datetime**: CreatedOn field from CSV (sequential)
- **Batch ID**: Batch Id field from CSV (sequential)
- **Prediction Values** (random):
  - L_color_value
  - R_color_Value (mapped from C_color_value in CSV)
  - C_color_value (mapped from H_color_value in CSV)

## Files Created

### Backend Files

1. **`Backend/src/server/controllers/blends.controller.js`**
   - Contains API controllers for fetching blend data
   - Implements CSV reading with caching for performance
   - Provides endpoints for random data, paginated data, and cache refresh

2. **`Backend/src/server/routes/blends.routes.js`**
   - Defines API routes for blend data endpoints
   - Routes: `/random`, `/all`, `/refresh`

3. **`Backend/src/server/app.js`** (modified)
   - Added import and route registration for blends API
   - Route: `/api/v1/blends`

### Frontend Files

4. **`Frontend/src/components/tables/BlendsPredictionTable.tsx`**
   - Main table component with auto-refresh functionality
   - Displays data in Ant Design table format
   - Configurable refresh interval and row count

5. **`Frontend/src/components/tables/BlendsPredictionTable.css`**
   - Styling for the table component
   - Responsive design and animations

6. **`Frontend/src/components/Dashboard/BlendsPredictionDemo.tsx`**
   - Demo page with control panel
   - Allows configuration of refresh settings
   - Provides usage instructions and statistics

7. **`Frontend/src/components/Dashboard/Insight Section/InsightTabContent.tsx`** (modified)
   - Added new "Blends Prediction" tab
   - Integrated BlendsPredictionDemo component

## API Endpoints

### GET `/api/v1/blends/random`
Fetches data with sequential datetime/batch_id and random color values from the CSV file.

**Query Parameters:**
- `count` (optional): Number of rows to fetch (default: 5, max: 20)

**Response:**
```json
{
  "success": true,
  "message": "Random blend data fetched successfully",
  "data": [
    {
      "datetime": "7/16/2024 3:42",
      "batch_id": "765878970",
      "L_color_value": 32.745,
      "R_color_value": 75.15,
      "C_color_value": 17.65652174,
      "moist": 6.992372372,
      "ffa": 2.702042042,
      "iv": 34.84084084,
      "ph": 5.343153153,
      "fat": 44.58942943,
      "cluster": "Cluster 0",
      "quality": 0
    }
  ]
}
```

### GET `/api/v1/blends/all`
Fetches all blend data with pagination.

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Records per page (default: 10)

### POST `/api/v1/blends/refresh`
Refreshes the CSV data cache.

## Usage

### Accessing the Component

1. Navigate to the Insight section of the dashboard
2. Click on the "Blends Prediction" tab
3. The table will automatically start fetching and displaying data

### Configuration Options

The demo page provides controls for:
- **Auto Refresh Toggle**: Enable/disable automatic updates
- **Refresh Interval**: Set update frequency (1-60 seconds)
- **Rows per Fetch**: Number of random rows to fetch (1-20)
- **Manual Refresh**: Trigger immediate data fetch

### Component Props

```typescript
interface BlendsPredictionTableProps {
  refreshInterval?: number; // in milliseconds, default 10000 (10 seconds)
  rowCount?: number; // number of random rows to fetch, default 5
}
```

## Features

- **Real-time Updates**: Automatically fetches new data every 10 seconds (configurable)
- **Random Data Selection**: Shows different data on each refresh
- **Performance Optimized**: CSV data is cached in memory for 5 minutes
- **Responsive Design**: Works on desktop and mobile devices
- **Error Handling**: Graceful error handling with user feedback
- **Data Persistence**: Keeps last 20 rows in the table to show data history

## Installation & Setup

1. **Install Dependencies**:
   ```bash
   cd Backend
   npm install csv-parser
   ```

2. **Verify CSV File**: Ensure `Backend/uploads/blends_1.csv` exists

3. **Test Backend**:
   ```bash
   cd Backend
   node test-blends-api.js
   ```

4. **Start Application**: The component will be available in the Insight section

## Data Mapping

| CSV Column | Table Column | Description |
|------------|--------------|-------------|
| CreatedOn | datetime | Timestamp of the record |
| Batch Id | batch_id | Unique batch identifier |
| L_color_value | L_color_value | L color value |
| C_color_value | R_color_Value | C color mapped to R color |
| H_color_value | C_color_value | H color mapped to C color |

## Performance Considerations

- CSV data is cached in memory for 5 minutes to reduce file I/O
- Table displays maximum 20 rows to prevent memory issues
- Configurable refresh intervals to balance real-time updates with performance
- Efficient random selection algorithm

## Troubleshooting

1. **CSV File Not Found**: Ensure `blends_1.csv` exists in `Backend/uploads/`
2. **No Data Displayed**: Check browser console for API errors
3. **Slow Performance**: Increase refresh interval or reduce row count
4. **Memory Issues**: Clear browser cache or restart the application

## Future Enhancements

- Add data filtering and search capabilities
- Implement data export functionality
- Add more visualization options (charts, graphs)
- Support for multiple CSV files
- Real-time notifications for new data
